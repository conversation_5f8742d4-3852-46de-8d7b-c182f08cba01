"use client"

import { MainLayout } from '@/components/layout/main-layout'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Settings, 
  Share, 
  MessageCircle, 
  Heart, 
  Play, 
  Music, 
  Video, 
  Image as ImageIcon,
  Users,
  TrendingUp,
  Calendar,
  MapPin,
  Link as LinkIcon,
  Instagram,
  Twitter,
  Youtube,
} from 'lucide-react'

const userStats = {
  followers: 12500,
  following: 234,
  posts: 89,
  likes: 45600,
}

const recentPosts = [
  {
    id: 1,
    type: 'music',
    title: 'Midnight Reflections',
    thumbnail: '/placeholder-thumbnail.jpg',
    views: 1250,
    likes: 89,
    duration: '3:24',
    mood: 'peaceful',
  },
  {
    id: 2,
    type: 'video',
    title: 'Studio Session Behind the Scenes',
    thumbnail: '/placeholder-thumbnail.jpg',
    views: 890,
    likes: 67,
    duration: '5:12',
    mood: 'inspired',
  },
  {
    id: 3,
    type: 'image',
    title: 'New Album Artwork Reveal',
    thumbnail: '/placeholder-thumbnail.jpg',
    views: 2100,
    likes: 156,
    mood: 'excited',
  },
]

const moodStats = {
  happy: 25,
  chill: 30,
  inspired: 20,
  peaceful: 15,
  energetic: 10,
}

export default function ProfilePage() {
  return (
    <MainLayout>
      <div className="h-full overflow-y-auto bg-background">
        <div className="max-w-4xl mx-auto p-6 space-y-6">
          {/* Profile Header */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
                <Avatar className="h-24 w-24">
                  <AvatarImage src="/placeholder-avatar.jpg" />
                  <AvatarFallback>JD</AvatarFallback>
                </Avatar>
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-2">
                    <h1 className="text-2xl font-bold">John Doe</h1>
                    <Badge variant="secondary">Creator</Badge>
                    <Badge className="hvppy-gradient">Verified</Badge>
                  </div>
                  
                  <p className="text-muted-foreground">@johndoe</p>
                  
                  <p className="text-sm max-w-md">
                    Singer-songwriter creating vibes for every mood 🎵 
                    Spreading positivity through music ✨ 
                    New album coming soon!
                  </p>
                  
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      Los Angeles, CA
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Joined March 2023
                    </div>
                    <div className="flex items-center">
                      <LinkIcon className="h-4 w-4 mr-1" />
                      johndoemusic.com
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Instagram className="h-4 w-4 mr-2" />
                      Instagram
                    </Button>
                    <Button size="sm" variant="outline">
                      <Twitter className="h-4 w-4 mr-2" />
                      Twitter
                    </Button>
                    <Button size="sm" variant="outline">
                      <Youtube className="h-4 w-4 mr-2" />
                      YouTube
                    </Button>
                  </div>
                </div>
                
                <div className="flex flex-col space-y-2">
                  <Button className="hvppy-gradient">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Message
                  </Button>
                  <Button variant="outline">
                    <Share className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{userStats.followers.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Followers</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{userStats.following.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Following</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{userStats.posts}</div>
                <div className="text-sm text-muted-foreground">Posts</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{userStats.likes.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Total Likes</div>
              </CardContent>
            </Card>
          </div>

          {/* Content Tabs */}
          <Tabs defaultValue="posts" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="posts">Posts</TabsTrigger>
              <TabsTrigger value="moods">Moods</TabsTrigger>
              <TabsTrigger value="memories">Memories</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="posts" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recentPosts.map((post) => (
                  <Card key={post.id} className="group cursor-pointer hover:shadow-lg transition-shadow">
                    <CardContent className="p-0">
                      <div className="relative aspect-square bg-gray-100 rounded-t-lg overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                        <div className="absolute bottom-2 left-2 right-2">
                          <div className="flex items-center justify-between text-white text-sm">
                            <div className="flex items-center space-x-1">
                              {post.type === 'music' && <Music className="h-4 w-4" />}
                              {post.type === 'video' && <Video className="h-4 w-4" />}
                              {post.type === 'image' && <ImageIcon className="h-4 w-4" />}
                              <span>{post.duration || 'Image'}</span>
                            </div>
                            <Badge className={`mood-${post.mood} text-xs`}>
                              {post.mood}
                            </Badge>
                          </div>
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button size="sm" className="hvppy-gradient">
                            <Play className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="p-3">
                        <h3 className="font-medium text-sm mb-2">{post.title}</h3>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{post.views.toLocaleString()} views</span>
                          <div className="flex items-center space-x-1">
                            <Heart className="h-3 w-3" />
                            <span>{post.likes}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="moods" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Mood Distribution</CardTitle>
                  <CardDescription>Your content mood breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(moodStats).map(([mood, percentage]) => (
                      <div key={mood} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full mood-${mood}`} />
                          <span className="capitalize">{mood}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-24 h-2 bg-muted rounded-full overflow-hidden">
                            <div 
                              className={`h-full mood-${mood}`}
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                          <span className="text-sm text-muted-foreground w-8">{percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="memories" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Fan Memories</CardTitle>
                  <CardDescription>Moments your fans have saved</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Heart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No memories saved yet</p>
                    <p className="text-sm text-muted-foreground">
                      When fans save moments from your content, they'll appear here
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2" />
                      Growth
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>This week</span>
                        <span className="text-green-600">+12.5%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>This month</span>
                        <span className="text-green-600">+34.2%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>All time</span>
                        <span className="text-muted-foreground">12.5K followers</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      Engagement
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Avg. likes per post</span>
                        <span>89</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Engagement rate</span>
                        <span>8.4%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Top mood</span>
                        <span className="mood-chill px-2 py-1 rounded text-xs">Chill</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MainLayout>
  )
}
