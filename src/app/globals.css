@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 204 12.1951% 91.9608%;
    --foreground: 0 0% 20%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 20%;
    --primary: 13.2143 73.0435% 54.902%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 14.2857% 95.8824%;
    --secondary-foreground: 215 13.7931% 34.1176%;
    --muted: 210 20% 98.0392%;
    --muted-foreground: 220 8.9362% 46.0784%;
    --accent: 207.6923 46.4286% 89.0196%;
    --accent-foreground: 224.4444 64.2857% 32.9412%;
    --destructive: 0 84.2365% 60.1961%;
    --destructive-foreground: 0 0% 100%;
    --border: 210 9.375% 87.451%;
    --input: 220 15.7895% 96.2745%;
    --ring: 13.2143 73.0435% 54.902%;
    --chart-1: 210 37.5% 65.4902%;
    --chart-2: 12.9032 73.2283% 75.098%;
    --chart-3: 213.1579 29.9213% 50.1961%;
    --chart-4: 216.9231 35.7798% 42.7451%;
    --chart-5: 221.0127 43.6464% 35.4902%;
    --sidebar: 216 7.9365% 87.6471%;
    --sidebar-foreground: 0 0% 20%;
    --sidebar-primary: 13.2143 73.0435% 54.902%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 207.6923 46.4286% 89.0196%;
    --sidebar-accent-foreground: 224.4444 64.2857% 32.9412%;
    --sidebar-border: 220 13.0435% 90.9804%;
    --sidebar-ring: 13.2143 73.0435% 54.902%;
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0em;
    --spacing: 0.25rem;
  }

  .dark {
    --background: 219.1304 29.1139% 15.4902%;
    --foreground: 0 0% 89.8039%;
    --card: 223.6364 20.7547% 20.7843%;
    --card-foreground: 0 0% 89.8039%;
    --popover: 223.3333 19.1489% 18.4314%;
    --popover-foreground: 0 0% 89.8039%;
    --primary: 13.2143 73.0435% 54.902%;
    --primary-foreground: 0 0% 100%;
    --secondary: 222 19.2308% 20.3922%;
    --secondary-foreground: 0 0% 89.8039%;
    --muted: 222 19.2308% 20.3922%;
    --muted-foreground: 0 0% 63.9216%;
    --accent: 223.6364 34.375% 25.098%;
    --accent-foreground: 213.3333 96.9231% 87.2549%;
    --destructive: 0 84.2365% 60.1961%;
    --destructive-foreground: 0 0% 100%;
    --border: 224.3478 15.8621% 28.4314%;
    --input: 224.3478 15.8621% 28.4314%;
    --ring: 13.2143 73.0435% 54.902%;
    --chart-1: 210 37.5% 65.4902%;
    --chart-2: 11.7241 63.5036% 73.1373%;
    --chart-3: 213.1579 29.9213% 50.1961%;
    --chart-4: 216.9231 35.7798% 42.7451%;
    --chart-5: 221.0127 43.6464% 35.4902%;
    --sidebar: 222.8571 20% 20.5882%;
    --sidebar-foreground: 0 0% 89.8039%;
    --sidebar-primary: 13.2143 73.0435% 54.902%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 223.6364 34.375% 25.098%;
    --sidebar-accent-foreground: 213.3333 96.9231% 87.2549%;
    --sidebar-border: 224.3478 15.8621% 28.4314%;
    --sidebar-ring: 13.2143 73.0435% 54.902%;
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom HVPPY Central styles */
@layer components {
  .hvppy-gradient {
    @apply bg-gradient-to-r from-hvppy-500 to-hvppy-700;
  }

  .mood-happy {
    @apply bg-mood-happy text-black;
  }

  .mood-chill {
    @apply bg-mood-chill text-black;
  }

  .mood-heartbroken {
    @apply bg-mood-heartbroken text-white;
  }

  .mood-inspired {
    @apply bg-mood-inspired text-white;
  }

  .mood-energetic {
    @apply bg-mood-energetic text-white;
  }

  .mood-peaceful {
    @apply bg-mood-peaceful text-black;
  }

  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .content-card {
    @apply bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow;
  }

  .creator-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-hvppy-100 text-hvppy-800 dark:bg-hvppy-900 dark:text-hvppy-200;
  }
}
