"use client"

import { MainLayout } from '@/components/layout/main-layout'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Video, 
  Music, 
  Image as ImageIcon, 
  BarChart3, 
  Users, 
  Heart, 
  TrendingUp,
  Calendar,
  Clock,
  Eye,
  MessageSquare,
  Settings,
  Palette,
  Zap,
  Lightbulb,
} from 'lucide-react'

const quickStats = {
  totalViews: 45600,
  totalLikes: 3420,
  followers: 12500,
  engagement: 8.4,
}

const recentUploads = [
  {
    id: 1,
    title: 'Midnight Reflections',
    type: 'music',
    status: 'published',
    views: 1250,
    likes: 89,
    uploadedAt: '2 hours ago',
    mood: 'peaceful',
  },
  {
    id: 2,
    title: 'Studio Session Vibes',
    type: 'video',
    status: 'processing',
    views: 0,
    likes: 0,
    uploadedAt: '30 minutes ago',
    mood: 'inspired',
  },
  {
    id: 3,
    title: 'AI Collaboration Experiment',
    type: 'music',
    status: 'draft',
    views: 0,
    likes: 0,
    uploadedAt: '1 day ago',
    mood: 'experimental',
  },
]

const aiSuggestions = [
  {
    type: 'mood',
    title: 'Trending Mood: Nostalgic',
    description: 'Content tagged with "nostalgic" is performing 25% better this week',
    action: 'Create nostalgic content',
  },
  {
    type: 'time',
    title: 'Optimal Upload Time',
    description: 'Your audience is most active at 7 PM PST',
    action: 'Schedule for 7 PM',
  },
  {
    type: 'collaboration',
    title: 'Collaboration Opportunity',
    description: 'Maya Chen has similar audience overlap (78%)',
    action: 'Send collaboration request',
  },
]

export default function CreatorStudioPage() {
  return (
    <MainLayout>
      <div className="h-full overflow-y-auto bg-background">
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Creator Studio</h1>
              <p className="text-muted-foreground">Manage your content and grow your audience</p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button className="hvppy-gradient">
                <Upload className="h-4 w-4 mr-2" />
                Upload Content
              </Button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Views</p>
                    <p className="text-2xl font-bold">{quickStats.totalViews.toLocaleString()}</p>
                  </div>
                  <Eye className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Likes</p>
                    <p className="text-2xl font-bold">{quickStats.totalLikes.toLocaleString()}</p>
                  </div>
                  <Heart className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Followers</p>
                    <p className="text-2xl font-bold">{quickStats.followers.toLocaleString()}</p>
                  </div>
                  <Users className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Engagement</p>
                    <p className="text-2xl font-bold">{quickStats.engagement}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Get started with creating content</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Button variant="outline" className="h-20 flex-col">
                      <Music className="h-6 w-6 mb-2" />
                      Upload Music
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <Video className="h-6 w-6 mb-2" />
                      Upload Video
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <ImageIcon className="h-6 w-6 mb-2" />
                      Upload Image
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <MessageSquare className="h-6 w-6 mb-2" />
                      Go Live
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Uploads */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Uploads</CardTitle>
                  <CardDescription>Your latest content and performance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentUploads.map((upload) => (
                      <div key={upload.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                            {upload.type === 'music' && <Music className="h-6 w-6" />}
                            {upload.type === 'video' && <Video className="h-6 w-6" />}
                            {upload.type === 'image' && <ImageIcon className="h-6 w-6" />}
                          </div>
                          <div>
                            <h3 className="font-medium">{upload.title}</h3>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <Badge 
                                variant={upload.status === 'published' ? 'default' : 
                                        upload.status === 'processing' ? 'secondary' : 'outline'}
                              >
                                {upload.status}
                              </Badge>
                              <span>•</span>
                              <span>{upload.uploadedAt}</span>
                              <span>•</span>
                              <Badge className={`mood-${upload.mood} text-xs`}>
                                {upload.mood}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="text-center">
                            <p className="font-medium">{upload.views.toLocaleString()}</p>
                            <p className="text-muted-foreground">Views</p>
                          </div>
                          <div className="text-center">
                            <p className="font-medium">{upload.likes}</p>
                            <p className="text-muted-foreground">Likes</p>
                          </div>
                          <Button variant="ghost" size="sm">
                            <BarChart3 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* AI Suggestions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Zap className="h-5 w-5 mr-2 text-hvppy-500" />
                    AI Suggestions
                  </CardTitle>
                  <CardDescription>Powered by HVPPY AI</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {aiSuggestions.map((suggestion, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-start space-x-2">
                          <Lightbulb className="h-4 w-4 mt-1 text-hvppy-500" />
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{suggestion.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1">
                              {suggestion.description}
                            </p>
                            <Button variant="link" size="sm" className="p-0 h-auto mt-2">
                              {suggestion.action}
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Content Calendar */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Content Calendar
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Today</span>
                      <Badge variant="outline">2 scheduled</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Tomorrow</span>
                      <Badge variant="outline">1 scheduled</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>This week</span>
                      <Badge variant="outline">5 scheduled</Badge>
                    </div>
                    <Button variant="outline" size="sm" className="w-full">
                      <Calendar className="h-4 w-4 mr-2" />
                      View Calendar
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Mood Trends */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Palette className="h-5 w-5 mr-2" />
                    Trending Moods
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Badge className="mood-nostalgic">Nostalgic</Badge>
                      <span className="text-sm text-green-600">+25%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <Badge className="mood-chill">Chill</Badge>
                      <span className="text-sm text-green-600">+18%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <Badge className="mood-inspired">Inspired</Badge>
                      <span className="text-sm text-green-600">+12%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <Badge className="mood-happy">Happy</Badge>
                      <span className="text-sm text-red-600">-5%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
