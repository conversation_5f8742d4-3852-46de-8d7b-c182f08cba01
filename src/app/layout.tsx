import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/providers/theme-provider"
import { Toaster } from "@/components/ui/sonner"

const inter = Inter({ subsets: ["latin"], variable: "--font-inter" })

export const metadata: Metadata = {
  title: "HVPPY Central - Media Content Sharing Platform",
  description: "A platform for artists and content creators with emotional-AI fan engagement, persona-based creator channels, and powerful content management tools.",
  keywords: ["content sharing", "artists", "creators", "AI", "social media", "fan engagement"],
  authors: [{ name: "HVPPY Central Team" }],
  openGraph: {
    title: "HVPPY Central",
    description: "The future of content sharing and fan engagement",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "HVPPY Central",
    description: "The future of content sharing and fan engagement",
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
