"use client"

import { VerticalFeedContainer } from '@/components/feed/vertical-feed-container'
import { FeedType, InteractionData } from '@/types/feed'

export default function MoodFeedPage() {
  const handleItemChange = (item: any, index: number) => {
    console.log('Mood feed - Current item changed:', { item: item.post.title, index })
  }

  const handleInteraction = (interaction: InteractionData) => {
    console.log('Mood feed - User interaction:', interaction)
  }

  return (
    <div className="h-full bg-black">
      <VerticalFeedContainer
        feedType={FeedType.MOOD_BASED}
        onItemChange={handleItemChange}
        onInteraction={handleInteraction}
        autoPlay={true}
        className="h-full"
      />
    </div>
  )
}
