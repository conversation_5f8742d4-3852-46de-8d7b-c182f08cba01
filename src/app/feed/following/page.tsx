"use client"

import { VerticalFeedContainer } from '@/components/feed/vertical-feed-container'
import { FeedType, InteractionData } from '@/types/feed'

export default function FollowingFeedPage() {
  const handleItemChange = (item: any, index: number) => {
    console.log('Following feed - Current item changed:', { item: item.post.title, index })
  }

  const handleInteraction = (interaction: InteractionData) => {
    console.log('Following feed - User interaction:', interaction)
  }

  return (
    <div className="h-full bg-black">
      <VerticalFeedContainer
        feedType={FeedType.FOLLOWING}
        onItemChange={handleItemChange}
        onInteraction={handleInteraction}
        autoPlay={true}
        className="h-full"
      />
    </div>
  )
}
