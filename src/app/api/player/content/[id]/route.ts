import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { createMediaSource } from '@/lib/player/utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Load content classification
    const contentClassificationPath = path.join(process.cwd(), 'public/hvppy-content/content-classification.json')
    const classificationData = await fs.readFile(contentClassificationPath, 'utf-8')
    const classification = JSON.parse(classificationData)
    
    // Find the content item
    let contentItem: any = null
    let categoryName: string = ''
    
    // Search through all categories
    for (const [catName, category] of Object.entries(classification.categories)) {
      if (Array.isArray((category as any).items)) {
        const item = (category as any).items.find((item: any) => item.id === id)
        if (item) {
          contentItem = item
          categoryName = catName
          break
        }
      }
    }
    
    // Check featured content
    if (!contentItem && classification.featured?.id === id) {
      contentItem = classification.featured
      categoryName = 'featured'
    }
    
    if (!contentItem) {
      return NextResponse.json(
        { error: 'Content not found' },
        { status: 404 }
      )
    }

    // Create media source with streaming URL
    const streamUrl = `/api/player/stream/${id}`
    const source = createMediaSource(
      id,
      streamUrl,
      {
        type: contentItem.contentType === 'audio' ? 'audio' : 'video',
        format: contentItem.format || path.extname(contentItem.filename).slice(1),
        quality: contentItem.quality || 'auto',
        duration: contentItem.duration || contentItem.durationFormatted ? 
          parseDuration(contentItem.durationFormatted) : undefined,
        size: contentItem.fileSize || contentItem.fileSizeFormatted ? 
          parseFileSize(contentItem.fileSizeFormatted) : undefined,
        bitrate: contentItem.bitRate
      }
    )

    // Prepare metadata
    const metadata = {
      id: contentItem.id,
      title: contentItem.title,
      artist: 'HVPPY',
      album: categoryName,
      genre: 'Hip Hop',
      year: new Date(contentItem.createdDate || '2023-01-01').getFullYear(),
      duration: source.duration || 0,
      thumbnail: contentItem.thumbnailUrl,
      artwork: contentItem.thumbnailUrl,
      description: contentItem.description || contentItem.title,
      tags: contentItem.tags || [],
      moods: contentItem.moods || [],
      featuring: contentItem.featuring || [],
      youtubeId: contentItem.youtubeId,
      category: categoryName,
      contentType: contentItem.contentType,
      relatedAudio: contentItem.relatedAudio,
      relatedVideo: contentItem.relatedVideo,
      series: contentItem.series,
      episode: contentItem.episode,
      location: contentItem.location
    }

    // Prepare response
    const response = {
      id,
      source,
      metadata,
      category: categoryName,
      streamUrl,
      downloadUrl: `/hvppy-content/${contentItem.filename}`, // Direct download
      thumbnailUrl: contentItem.thumbnailUrl,
      
      // Technical details
      technical: {
        format: source.format,
        duration: source.duration,
        size: source.size,
        bitrate: source.bitrate,
        codec: contentItem.videoCodec || contentItem.audioCodec,
        resolution: contentItem.resolution,
        aspectRatio: contentItem.aspectRatio,
        frameRate: contentItem.frameRate
      },
      
      // Related content
      related: await getRelatedContent(contentItem, classification)
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Content API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get related content based on moods, tags, or series
async function getRelatedContent(contentItem: any, classification: any): Promise<any[]> {
  const related: any[] = []
  const maxRelated = 5
  
  try {
    // Find content with similar moods
    const itemMoods = contentItem.moods || []
    
    for (const [catName, category] of Object.entries(classification.categories)) {
      if (Array.isArray((category as any).items)) {
        for (const item of (category as any).items) {
          if (item.id === contentItem.id) continue // Skip self
          
          const itemHasSimilarMood = item.moods?.some((mood: string) => 
            itemMoods.includes(mood)
          )
          
          const itemInSameSeries = item.series && item.series === contentItem.series
          
          if (itemHasSimilarMood || itemInSameSeries) {
            related.push({
              id: item.id,
              title: item.title,
              contentType: item.contentType,
              thumbnailUrl: item.thumbnailUrl,
              moods: item.moods,
              category: catName,
              streamUrl: `/api/player/stream/${item.id}`
            })
            
            if (related.length >= maxRelated) break
          }
        }
        if (related.length >= maxRelated) break
      }
    }
  } catch (error) {
    console.error('Error finding related content:', error)
  }
  
  return related
}

// Parse duration string (e.g., "3:38" -> 218 seconds)
function parseDuration(durationStr: string): number {
  if (!durationStr) return 0
  
  const parts = durationStr.split(':').map(Number)
  if (parts.length === 2) {
    return parts[0] * 60 + parts[1] // MM:SS
  } else if (parts.length === 3) {
    return parts[0] * 3600 + parts[1] * 60 + parts[2] // HH:MM:SS
  }
  
  return 0
}

// Parse file size string (e.g., "9.5MB" -> bytes)
function parseFileSize(sizeStr: string): number {
  if (!sizeStr) return 0
  
  const match = sizeStr.match(/^([\d.]+)\s*(KB|MB|GB)$/i)
  if (!match) return 0
  
  const value = parseFloat(match[1])
  const unit = match[2].toUpperCase()
  
  switch (unit) {
    case 'KB':
      return value * 1024
    case 'MB':
      return value * 1024 * 1024
    case 'GB':
      return value * 1024 * 1024 * 1024
    default:
      return value
  }
}
