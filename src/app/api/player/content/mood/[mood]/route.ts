import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ mood: string }> }
) {
  try {
    const { mood } = await params
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')
    const contentType = searchParams.get('type') // 'audio', 'video', or null for all
    
    // Load content classification
    const contentClassificationPath = path.join(process.cwd(), 'public/hvppy-content/content-classification.json')
    const classificationData = await fs.readFile(contentClassificationPath, 'utf-8')
    const classification = JSON.parse(classificationData)
    
    // Find all content items with the specified mood
    const matchingContent: any[] = []
    
    // Search through all categories
    for (const [catName, category] of Object.entries(classification.categories)) {
      if (Array.isArray((category as any).items)) {
        for (const item of (category as any).items) {
          const itemMoods = item.moods || []
          
          // Check if item has the requested mood
          if (itemMoods.includes(mood)) {
            // Filter by content type if specified
            if (contentType && item.contentType !== contentType) {
              continue
            }
            
            matchingContent.push({
              ...item,
              category: catName,
              streamUrl: `/api/player/stream/${item.id}`,
              source: {
                id: item.id,
                url: `/api/player/stream/${item.id}`,
                type: item.contentType === 'audio' ? 'audio' : 'video',
                format: item.format || path.extname(item.filename).slice(1),
                quality: item.quality || 'auto'
              }
            })
          }
        }
      }
    }
    
    // Check featured content
    if (classification.featured?.moods?.includes(mood)) {
      const featured = classification.featured
      if (!contentType || featured.contentType === contentType) {
        matchingContent.unshift({ // Add to beginning
          ...featured,
          category: 'featured',
          streamUrl: `/api/player/stream/${featured.id}`,
          source: {
            id: featured.id,
            url: `/api/player/stream/${featured.id}`,
            type: featured.contentType === 'audio' ? 'audio' : 'video',
            format: featured.format || 'webm',
            quality: featured.quality || 'auto'
          }
        })
      }
    }
    
    if (matchingContent.length === 0) {
      return NextResponse.json({
        mood,
        items: [],
        total: 0,
        limit,
        offset,
        hasMore: false
      })
    }
    
    // Sort by relevance (featured first, then by category priority)
    const categoryPriority: Record<string, number> = {
      featured: 0,
      music_videos: 1,
      visualizers: 2,
      audio: 3,
      documentary: 4,
      promotional: 5,
      interviews: 6
    }
    
    matchingContent.sort((a, b) => {
      const aPriority = categoryPriority[a.category] ?? 999
      const bPriority = categoryPriority[b.category] ?? 999
      return aPriority - bPriority
    })
    
    // Apply pagination
    const paginatedContent = matchingContent.slice(offset, offset + limit)
    
    // Prepare response
    const response = {
      mood,
      items: paginatedContent.map(item => ({
        id: item.id,
        title: item.title,
        contentType: item.contentType,
        category: item.category,
        moods: item.moods,
        tags: item.tags,
        thumbnailUrl: item.thumbnailUrl,
        streamUrl: item.streamUrl,
        source: item.source,
        duration: item.duration || item.durationFormatted,
        featuring: item.featuring,
        youtubeId: item.youtubeId,
        metadata: {
          artist: 'HVPPY',
          album: item.category,
          genre: 'Hip Hop',
          description: item.description || item.title,
          series: item.series,
          episode: item.episode
        }
      })),
      total: matchingContent.length,
      limit,
      offset,
      hasMore: offset + limit < matchingContent.length,
      
      // Mood information
      moodInfo: classification.moods?.[mood] || {
        description: `Content tagged with ${mood} mood`,
        color: '#8B5CF6',
        count: matchingContent.length
      }
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Mood content API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get available moods
export async function OPTIONS(request: NextRequest) {
  try {
    // Load content classification
    const contentClassificationPath = path.join(process.cwd(), 'public/hvppy-content/content-classification.json')
    const classificationData = await fs.readFile(contentClassificationPath, 'utf-8')
    const classification = JSON.parse(classificationData)
    
    // Extract all unique moods
    const allMoods = new Set<string>()
    
    // From categories
    for (const category of Object.values(classification.categories)) {
      if (Array.isArray((category as any).items)) {
        for (const item of (category as any).items) {
          if (item.moods) {
            item.moods.forEach((mood: string) => allMoods.add(mood))
          }
        }
      }
    }
    
    // From featured content
    if (classification.featured?.moods) {
      classification.featured.moods.forEach((mood: string) => allMoods.add(mood))
    }
    
    const moodsWithInfo = Array.from(allMoods).map(mood => ({
      name: mood,
      ...(classification.moods?.[mood] || {
        description: `Content tagged with ${mood} mood`,
        color: '#8B5CF6'
      })
    }))
    
    return NextResponse.json({
      moods: moodsWithInfo,
      total: moodsWithInfo.length
    })
  } catch (error) {
    console.error('Moods API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
