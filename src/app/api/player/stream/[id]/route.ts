import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { stat } from 'fs/promises'

// Stream media files with range support for progressive loading
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Load content classification to get file path
    const contentClassificationPath = path.join(process.cwd(), 'public/hvppy-content/content-classification.json')
    const classificationData = await fs.readFile(contentClassificationPath, 'utf-8')
    const classification = JSON.parse(classificationData)
    
    // Find the content item
    let filePath: string | null = null
    let contentItem: any = null
    
    // Search through all categories
    for (const category of Object.values(classification.categories)) {
      if (Array.isArray((category as any).items)) {
        const item = (category as any).items.find((item: any) => item.id === id)
        if (item) {
          contentItem = item
          filePath = path.join(process.cwd(), 'public/hvppy-content', item.filename)
          break
        }
      }
    }
    
    if (!filePath || !contentItem) {
      return NextResponse.json(
        { error: 'Content not found' },
        { status: 404 }
      )
    }

    // Check if file exists
    try {
      await stat(filePath)
    } catch {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      )
    }

    const fileStats = await stat(filePath)
    const fileSize = fileStats.size
    
    // Get range header for partial content support
    const range = request.headers.get('range')
    
    if (range) {
      // Parse range header
      const parts = range.replace(/bytes=/, '').split('-')
      const start = parseInt(parts[0], 10)
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1
      const chunkSize = (end - start) + 1
      
      // Read file chunk
      const file = await fs.open(filePath, 'r')
      const buffer = Buffer.alloc(chunkSize)
      await file.read(buffer, 0, chunkSize, start)
      await file.close()
      
      // Determine content type
      const contentType = getContentType(filePath)
      
      return new NextResponse(buffer, {
        status: 206, // Partial Content
        headers: {
          'Content-Range': `bytes ${start}-${end}/${fileSize}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunkSize.toString(),
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': 'Range'
        }
      })
    } else {
      // Return full file
      const fileBuffer = await fs.readFile(filePath)
      const contentType = getContentType(filePath)
      
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Length': fileSize.toString(),
          'Content-Type': contentType,
          'Accept-Ranges': 'bytes',
          'Cache-Control': 'public, max-age=31536000',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': 'Range'
        }
      })
    }
  } catch (error) {
    console.error('Stream error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle HEAD requests for metadata
export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Load content classification
    const contentClassificationPath = path.join(process.cwd(), 'public/hvppy-content/content-classification.json')
    const classificationData = await fs.readFile(contentClassificationPath, 'utf-8')
    const classification = JSON.parse(classificationData)
    
    // Find the content item
    let filePath: string | null = null
    
    for (const category of Object.values(classification.categories)) {
      if (Array.isArray((category as any).items)) {
        const item = (category as any).items.find((item: any) => item.id === id)
        if (item) {
          filePath = path.join(process.cwd(), 'public/hvppy-content', item.filename)
          break
        }
      }
    }
    
    if (!filePath) {
      return new NextResponse(null, { status: 404 })
    }

    const fileStats = await stat(filePath)
    const contentType = getContentType(filePath)
    
    return new NextResponse(null, {
      headers: {
        'Content-Length': fileStats.size.toString(),
        'Content-Type': contentType,
        'Accept-Ranges': 'bytes',
        'Cache-Control': 'public, max-age=31536000',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } catch (error) {
    return new NextResponse(null, { status: 500 })
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Range, Content-Type',
      'Access-Control-Max-Age': '86400'
    }
  })
}

function getContentType(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase()
  
  const mimeTypes: Record<string, string> = {
    '.mp4': 'video/mp4',
    '.webm': 'video/webm',
    '.ogg': 'video/ogg',
    '.avi': 'video/x-msvideo',
    '.mov': 'video/quicktime',
    '.mkv': 'video/x-matroska',
    '.mp3': 'audio/mpeg',
    '.wav': 'audio/wav',
    '.flac': 'audio/flac',
    '.aac': 'audio/aac',
    '.m4a': 'audio/mp4',
    '.wma': 'audio/x-ms-wma'
  }
  
  return mimeTypes[ext] || 'application/octet-stream'
}
