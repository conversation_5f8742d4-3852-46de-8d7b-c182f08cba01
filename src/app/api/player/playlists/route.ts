import { NextRequest, NextResponse } from 'next/server'

// In-memory storage for demo purposes
// In production, this would use a database
const playlists = new Map<string, any>()

// GET /api/player/playlists - Get all playlists for a user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId') || 'default'
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    // Filter playlists by user
    const userPlaylists = Array.from(playlists.values())
      .filter(playlist => playlist.userId === userId)
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    
    // Apply pagination
    const paginatedPlaylists = userPlaylists.slice(offset, offset + limit)
    
    return NextResponse.json({
      playlists: paginatedPlaylists,
      total: userPlaylists.length,
      limit,
      offset,
      hasMore: offset + limit < userPlaylists.length
    })
  } catch (error) {
    console.error('Get playlists error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/player/playlists - Create a new playlist
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, items = [], userId = 'default', isPublic = false } = body
    
    if (!name) {
      return NextResponse.json(
        { error: 'Playlist name is required' },
        { status: 400 }
      )
    }
    
    const playlistId = `playlist-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const now = new Date().toISOString()
    
    const playlist = {
      id: playlistId,
      name,
      description: description || '',
      items: items.map((item: any, index: number) => ({
        id: item.id || `item-${index}`,
        contentId: item.contentId,
        addedAt: now,
        addedBy: userId,
        ...item
      })),
      userId,
      isPublic,
      createdAt: now,
      updatedAt: now,
      currentIndex: 0,
      shuffle: false,
      repeat: 'none',
      totalDuration: 0, // Will be calculated
      itemCount: items.length
    }
    
    // Calculate total duration if items have duration
    playlist.totalDuration = playlist.items.reduce((total: number, item: any) => {
      return total + (item.duration || 0)
    }, 0)
    
    playlists.set(playlistId, playlist)
    
    return NextResponse.json(playlist, { status: 201 })
  } catch (error) {
    console.error('Create playlist error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/player/playlists - Update multiple playlists (batch operation)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { operations } = body
    
    if (!Array.isArray(operations)) {
      return NextResponse.json(
        { error: 'Operations array is required' },
        { status: 400 }
      )
    }
    
    const results = []
    
    for (const operation of operations) {
      const { type, playlistId, data } = operation
      
      switch (type) {
        case 'update':
          const playlist = playlists.get(playlistId)
          if (playlist) {
            const updatedPlaylist = {
              ...playlist,
              ...data,
              updatedAt: new Date().toISOString()
            }
            playlists.set(playlistId, updatedPlaylist)
            results.push({ success: true, playlistId, playlist: updatedPlaylist })
          } else {
            results.push({ success: false, playlistId, error: 'Playlist not found' })
          }
          break
          
        case 'delete':
          if (playlists.has(playlistId)) {
            playlists.delete(playlistId)
            results.push({ success: true, playlistId, deleted: true })
          } else {
            results.push({ success: false, playlistId, error: 'Playlist not found' })
          }
          break
          
        default:
          results.push({ success: false, playlistId, error: 'Unknown operation type' })
      }
    }
    
    return NextResponse.json({ results })
  } catch (error) {
    console.error('Batch playlist operation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/player/playlists - Delete all playlists for a user
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    // Find and delete user's playlists
    let deletedCount = 0
    for (const [id, playlist] of playlists.entries()) {
      if (playlist.userId === userId) {
        playlists.delete(id)
        deletedCount++
      }
    }
    
    return NextResponse.json({
      message: `Deleted ${deletedCount} playlists`,
      deletedCount
    })
  } catch (error) {
    console.error('Delete playlists error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
