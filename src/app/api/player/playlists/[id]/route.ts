import { NextRequest, NextResponse } from 'next/server'

// In-memory storage for demo purposes
// In production, this would use a database
const playlists = new Map<string, any>()

// GET /api/player/playlists/[id] - Get a specific playlist
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const playlist = playlists.get(id)
    
    if (!playlist) {
      return NextResponse.json(
        { error: 'Playlist not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(playlist)
  } catch (error) {
    console.error('Get playlist error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/player/playlists/[id] - Update a playlist
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    
    const playlist = playlists.get(id)
    if (!playlist) {
      return NextResponse.json(
        { error: 'Playlist not found' },
        { status: 404 }
      )
    }
    
    // Update playlist properties
    const updatedPlaylist = {
      ...playlist,
      ...body,
      id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    }
    
    // Recalculate metadata if items changed
    if (body.items) {
      updatedPlaylist.itemCount = body.items.length
      updatedPlaylist.totalDuration = body.items.reduce((total: number, item: any) => {
        return total + (item.duration || 0)
      }, 0)
    }
    
    playlists.set(id, updatedPlaylist)
    
    return NextResponse.json(updatedPlaylist)
  } catch (error) {
    console.error('Update playlist error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/player/playlists/[id] - Delete a playlist
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    if (!playlists.has(id)) {
      return NextResponse.json(
        { error: 'Playlist not found' },
        { status: 404 }
      )
    }
    
    playlists.delete(id)
    
    return NextResponse.json({
      message: 'Playlist deleted successfully',
      id
    })
  } catch (error) {
    console.error('Delete playlist error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/player/playlists/[id] - Partial update (for specific operations)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { operation, data } = body
    
    const playlist = playlists.get(id)
    if (!playlist) {
      return NextResponse.json(
        { error: 'Playlist not found' },
        { status: 404 }
      )
    }
    
    let updatedPlaylist = { ...playlist }
    
    switch (operation) {
      case 'add_item':
        const newItem = {
          id: data.id || `item-${Date.now()}`,
          contentId: data.contentId,
          addedAt: new Date().toISOString(),
          addedBy: data.userId || 'default',
          ...data
        }
        updatedPlaylist.items = [...playlist.items, newItem]
        updatedPlaylist.itemCount = updatedPlaylist.items.length
        break
        
      case 'remove_item':
        updatedPlaylist.items = playlist.items.filter((item: any) => item.id !== data.itemId)
        updatedPlaylist.itemCount = updatedPlaylist.items.length
        break
        
      case 'reorder_items':
        const items = [...playlist.items]
        const [movedItem] = items.splice(data.fromIndex, 1)
        items.splice(data.toIndex, 0, movedItem)
        updatedPlaylist.items = items
        break
        
      case 'update_item':
        updatedPlaylist.items = playlist.items.map((item: any) => 
          item.id === data.itemId ? { ...item, ...data.updates } : item
        )
        break
        
      case 'set_current_index':
        updatedPlaylist.currentIndex = Math.max(0, Math.min(data.index, playlist.items.length - 1))
        break
        
      case 'toggle_shuffle':
        updatedPlaylist.shuffle = !playlist.shuffle
        if (updatedPlaylist.shuffle) {
          // Shuffle items (Fisher-Yates algorithm)
          const items = [...playlist.items]
          for (let i = items.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1))
            ;[items[i], items[j]] = [items[j], items[i]]
          }
          updatedPlaylist.items = items
          updatedPlaylist.currentIndex = 0
        }
        break
        
      case 'set_repeat':
        if (['none', 'one', 'all'].includes(data.mode)) {
          updatedPlaylist.repeat = data.mode
        }
        break
        
      case 'clear_items':
        updatedPlaylist.items = []
        updatedPlaylist.itemCount = 0
        updatedPlaylist.currentIndex = 0
        updatedPlaylist.totalDuration = 0
        break
        
      default:
        return NextResponse.json(
          { error: 'Unknown operation' },
          { status: 400 }
        )
    }
    
    // Recalculate total duration
    updatedPlaylist.totalDuration = updatedPlaylist.items.reduce((total: number, item: any) => {
      return total + (item.duration || 0)
    }, 0)
    
    updatedPlaylist.updatedAt = new Date().toISOString()
    playlists.set(id, updatedPlaylist)
    
    return NextResponse.json({
      playlist: updatedPlaylist,
      operation,
      success: true
    })
  } catch (error) {
    console.error('Patch playlist error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
