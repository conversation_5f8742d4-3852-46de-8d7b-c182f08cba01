import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { createMediaSource } from '@/lib/player/utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Load content classification
    const contentClassificationPath = path.join(process.cwd(), 'public/hvppy-content/content-classification.json')
    const classificationData = await fs.readFile(contentClassificationPath, 'utf-8')
    const classification = JSON.parse(classificationData)
    
    // Find the content item
    let contentItem: any = null
    let categoryName: string = ''
    
    // Check featured content first
    if (classification.featured && classification.featured.id === id) {
      contentItem = classification.featured
      categoryName = 'featured'
    }
    
    // Search in categories
    if (!contentItem) {
      for (const [catName, category] of Object.entries(classification.categories)) {
        if (Array.isArray((category as any).items)) {
          const item = (category as any).items.find((item: any) => item.id === id)
          if (item) {
            contentItem = item
            categoryName = catName
            break
          }
        }
      }
    }
    
    if (!contentItem) {
      return NextResponse.json(
        { error: 'Content not found' },
        { status: 404 }
      )
    }
    
    // Create media source
    const source = createMediaSource({
      url: `/api/player/stream/${id}`,
      type: contentItem.contentType === 'audio' ? 'audio' : 'video',
      format: contentItem.format || 'webm',
      duration: contentItem.duration || 0,
      size: contentItem.fileSize || 0,
      bitrate: contentItem.bitRate || 0
    })
    
    // Prepare metadata
    const metadata = {
      id: contentItem.id,
      title: contentItem.title,
      artist: contentItem.artist || 'HVPPY',
      album: contentItem.album,
      genre: contentItem.genre || 'Hip Hop',
      year: contentItem.year,
      duration: contentItem.duration || 0,
      durationFormatted: contentItem.durationFormatted,
      description: contentItem.description,
      tags: contentItem.tags || [],
      moods: contentItem.moods || [],
      featuring: contentItem.featuring || [],
      language: contentItem.language || 'eng',
      createdDate: contentItem.createdDate,
      youtubeId: contentItem.youtubeId,
      thumbnailUrl: contentItem.thumbnailUrl,
      relatedAudio: contentItem.relatedAudio
    }
    
    // Stream and download URLs
    const streamUrl = `/api/player/stream/${id}`
    const downloadUrl = `/hvppy-content/${contentItem.filename}`
    
    // Prepare response
    const response = {
      id,
      source,
      metadata,
      category: categoryName,
      streamUrl,
      downloadUrl,
      thumbnailUrl: contentItem.thumbnailUrl,
      
      // Technical details
      technical: {
        format: source.format,
        duration: source.duration,
        size: source.size,
        bitrate: source.bitrate,
        codec: contentItem.videoCodec || contentItem.audioCodec,
        resolution: contentItem.resolution,
        aspectRatio: contentItem.aspectRatio,
        frameRate: contentItem.frameRate
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Content API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
