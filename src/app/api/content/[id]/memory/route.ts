import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { CreateMemoryRequest } from '@/types/feed'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: postId } = await params
    const body: CreateMemoryRequest = await request.json()
    const { title, description, timestamp } = body

    // TODO: Get user ID from authentication
    const userId = 'temp-user-id' // Replace with actual auth

    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
    })

    if (!post) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      )
    }

    // Check if user already has a memory for this post
    const existingMemory = await prisma.memory.findFirst({
      where: {
        userId,
        postId,
      },
    })

    if (existingMemory) {
      return NextResponse.json(
        { error: 'Memory already exists for this post' },
        { status: 409 }
      )
    }

    // Create memory
    const memory = await prisma.memory.create({
      data: {
        userId,
        postId,
        title,
        description,
        timestamp,
      },
    })

    // Get updated post with counts
    const updatedPost = await prisma.post.findUnique({
      where: { id: postId },
      include: {
        _count: {
          select: {
            reactions: true,
            memories: true,
          },
        },
      },
    })

    return NextResponse.json({
      memory,
      post: updatedPost,
    })
  } catch (error) {
    console.error('Create memory API error:', error)
    return NextResponse.json(
      { error: 'Failed to create memory' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: postId } = await params
    
    // TODO: Get user ID from authentication
    const userId = 'temp-user-id' // Replace with actual auth

    // Find and delete memory
    const memory = await prisma.memory.findFirst({
      where: {
        userId,
        postId,
      },
    })

    if (!memory) {
      return NextResponse.json(
        { error: 'Memory not found' },
        { status: 404 }
      )
    }

    await prisma.memory.delete({
      where: {
        id: memory.id,
      },
    })

    // Get updated post with counts
    const updatedPost = await prisma.post.findUnique({
      where: { id: postId },
      include: {
        _count: {
          select: {
            reactions: true,
            memories: true,
          },
        },
      },
    })

    return NextResponse.json({
      post: updatedPost,
    })
  } catch (error) {
    console.error('Delete memory API error:', error)
    return NextResponse.json(
      { error: 'Failed to delete memory' },
      { status: 500 }
    )
  }
}
