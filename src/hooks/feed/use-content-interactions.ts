import { useCallback } from 'react'
import { useInteractionStore } from '@/lib/stores/interaction-store'
import { useFeedStore } from '@/lib/stores/feed-store'
import { ReactionType, MoodType } from '@/types'
import { CreateInteractionRequest, CreateMemoryRequest, UseContentInteractionsReturn } from '@/types/feed'
import { toast } from 'sonner'

export function useContentInteractions(): UseContentInteractionsReturn {
  const {
    reactions,
    memories,
    setReaction,
    removeReaction,
    addMemory,
    removeMemory,
    addShare,
    setReactionLoading,
    setMemoryLoading,
    setShareLoading,
    loadingReactions,
  } = useInteractionStore()

  const { updateItem } = useFeedStore()

  // React to a post
  const react = useCallback(async (
    postId: string, 
    type: ReactionType, 
    mood?: MoodType
  ) => {
    const currentReaction = reactions.get(postId)
    
    try {
      setReactionLoading(postId, true)

      // Optimistic update
      setReaction(postId, type)

      const request: CreateInteractionRequest = {
        postId,
        type,
        mood,
      }

      const response = await fetch(`/api/content/${postId}/interact`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error('Failed to react to post')
      }

      const result = await response.json()

      // Update feed item with new reaction count
      updateItem(postId, {
        post: {
          ...result.post,
          _count: {
            ...result.post._count,
            reactions: result.post._count.reactions,
          }
        }
      } as any)

      toast.success('Reaction added!')
    } catch (error) {
      // Revert optimistic update
      if (currentReaction) {
        setReaction(postId, currentReaction)
      } else {
        removeReaction(postId)
      }
      
      console.error('Error reacting to post:', error)
      toast.error('Failed to react to post')
    } finally {
      setReactionLoading(postId, false)
    }
  }, [reactions, setReaction, removeReaction, setReactionLoading, updateItem])

  // Remove reaction from a post
  const unreact = useCallback(async (postId: string) => {
    const currentReaction = reactions.get(postId)
    
    try {
      setReactionLoading(postId, true)

      // Optimistic update
      removeReaction(postId)

      const response = await fetch(`/api/content/${postId}/interact`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to remove reaction')
      }

      const result = await response.json()

      // Update feed item with new reaction count
      updateItem(postId, {
        post: {
          ...result.post,
          _count: {
            ...result.post._count,
            reactions: result.post._count.reactions,
          }
        }
      } as any)

      toast.success('Reaction removed')
    } catch (error) {
      // Revert optimistic update
      if (currentReaction) {
        setReaction(postId, currentReaction)
      }
      
      console.error('Error removing reaction:', error)
      toast.error('Failed to remove reaction')
    } finally {
      setReactionLoading(postId, false)
    }
  }, [reactions, removeReaction, setReaction, setReactionLoading, updateItem])

  // Toggle memory for a post
  const toggleMemory = useCallback(async (
    postId: string, 
    data?: CreateMemoryRequest
  ) => {
    const hasMemory = memories.has(postId)
    
    try {
      setMemoryLoading(postId, true)

      if (hasMemory) {
        // Remove memory - optimistic update
        removeMemory(postId)

        const response = await fetch(`/api/content/${postId}/memory`, {
          method: 'DELETE',
        })

        if (!response.ok) {
          throw new Error('Failed to remove memory')
        }

        toast.success('Memory removed')
      } else {
        // Add memory - optimistic update
        addMemory(postId)

        const request: CreateMemoryRequest = {
          postId,
          title: data?.title,
          description: data?.description,
          timestamp: data?.timestamp,
        }

        const response = await fetch(`/api/content/${postId}/memory`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(request),
        })

        if (!response.ok) {
          throw new Error('Failed to create memory')
        }

        toast.success('Memory saved!')
      }

      // Update feed item memory count
      const result = await fetch(`/api/content/${postId}`).then(res => res.json())
      updateItem(postId, {
        post: {
          ...result.post,
          _count: {
            ...result.post._count,
            memories: result.post._count.memories,
          }
        }
      } as any)

    } catch (error) {
      // Revert optimistic update
      if (hasMemory) {
        addMemory(postId)
      } else {
        removeMemory(postId)
      }
      
      console.error('Error toggling memory:', error)
      toast.error(hasMemory ? 'Failed to remove memory' : 'Failed to save memory')
    } finally {
      setMemoryLoading(postId, false)
    }
  }, [memories, addMemory, removeMemory, setMemoryLoading, updateItem])

  // Share a post
  const share = useCallback(async (postId: string) => {
    try {
      setShareLoading(postId, true)

      // Optimistic update
      addShare(postId)

      const response = await fetch(`/api/content/${postId}/share`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to share post')
      }

      // Update feed item share count
      const result = await response.json()
      updateItem(postId, {
        post: {
          ...result.post,
          shareCount: result.post.shareCount,
        }
      } as any)

      // Copy link to clipboard
      const shareUrl = `${window.location.origin}/post/${postId}`
      await navigator.clipboard.writeText(shareUrl)
      
      toast.success('Link copied to clipboard!')
    } catch (error) {
      console.error('Error sharing post:', error)
      toast.error('Failed to share post')
    } finally {
      setShareLoading(postId, false)
    }
  }, [addShare, setShareLoading, updateItem])



  return {
    reactions,
    memories,
    loading: loadingReactions,
    react,
    unreact,
    toggleMemory,
    share,
  }
}
