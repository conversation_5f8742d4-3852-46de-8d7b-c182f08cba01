import { useRef, useEffect, useState, useCallback } from 'react'
import { useFeedStore } from '@/lib/stores/feed-store'
import { UseInfiniteScrollReturn } from '@/types/feed'

interface UseInfiniteScrollOptions {
  threshold?: number // Percentage of item height to trigger next item
  onItemChange?: (index: number) => void
  autoAdvance?: boolean
  autoAdvanceDelay?: number
  preloadDistance?: number // Number of items to preload ahead
  offloadDistance?: number // Distance from viewport to offload content
  enableVirtualization?: boolean // Enable content virtualization for performance
}

export function useInfiniteScroll(
  options: UseInfiniteScrollOptions = {}
): UseInfiniteScrollReturn {
  const {
    threshold = 0.5,
    onItemChange,
    autoAdvance = false,
    autoAdvanceDelay = 5000,
    preloadDistance = 3,
    offloadDistance = 10,
    enableVirtualization = true,
  } = options

  const containerRef = useRef<HTMLDivElement>(null)
  const [isScrolling, setIsScrolling] = useState(false)
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null)
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()
  const lastScrollY = useRef(0)
  const autoAdvanceTimeoutRef = useRef<NodeJS.Timeout>()

  const {
    items,
    currentIndex,
    setCurrentIndex,
    hasMore,
  } = useFeedStore()

  // Handle scroll events
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return

    const container = containerRef.current
    const scrollTop = container.scrollTop
    const itemHeight = container.clientHeight
    const newIndex = Math.round(scrollTop / itemHeight)

    // Determine scroll direction
    if (scrollTop > lastScrollY.current) {
      setScrollDirection('down')
    } else if (scrollTop < lastScrollY.current) {
      setScrollDirection('up')
    }
    lastScrollY.current = scrollTop

    // Update current index if it changed
    if (newIndex !== currentIndex && newIndex >= 0 && newIndex < items.length) {
      setCurrentIndex(newIndex)
      onItemChange?.(newIndex)
    }

    // Set scrolling state
    setIsScrolling(true)
    
    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // Set timeout to detect end of scrolling
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false)
      setScrollDirection(null)
    }, 150)
  }, [currentIndex, items.length, setCurrentIndex, onItemChange])

  // Scroll to specific index
  const scrollToIndex = useCallback((index: number) => {
    if (!containerRef.current) return
    
    const container = containerRef.current
    const itemHeight = container.clientHeight
    const targetScrollTop = index * itemHeight

    container.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    })
  }, [])

  // Auto-advance functionality
  useEffect(() => {
    if (!autoAdvance || isScrolling) return

    autoAdvanceTimeoutRef.current = setTimeout(() => {
      const nextIndex = currentIndex + 1
      if (nextIndex < items.length) {
        scrollToIndex(nextIndex)
      }
    }, autoAdvanceDelay)

    return () => {
      if (autoAdvanceTimeoutRef.current) {
        clearTimeout(autoAdvanceTimeoutRef.current)
      }
    }
  }, [autoAdvance, autoAdvanceDelay, currentIndex, items.length, isScrolling, scrollToIndex])

  // Set up scroll listener
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      container.removeEventListener('scroll', handleScroll)
    }
  }, [handleScroll])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!containerRef.current) return

      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault()
          if (currentIndex > 0) {
            scrollToIndex(currentIndex - 1)
          }
          break
        case 'ArrowDown':
          event.preventDefault()
          if (currentIndex < items.length - 1) {
            scrollToIndex(currentIndex + 1)
          }
          break
        case 'Home':
          event.preventDefault()
          scrollToIndex(0)
          break
        case 'End':
          event.preventDefault()
          scrollToIndex(items.length - 1)
          break
      }
    }

    // Only add listener if container is focused or contains focus
    const container = containerRef.current
    if (container) {
      container.addEventListener('keydown', handleKeyDown)
      
      return () => {
        container.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [currentIndex, items.length, scrollToIndex])

  // Intersection Observer for better performance
  useEffect(() => {
    if (!containerRef.current) return

    const container = containerRef.current
    const items = container.children

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.intersectionRatio >= threshold) {
            const index = Array.from(items).indexOf(entry.target as Element)
            if (index !== -1 && index !== currentIndex) {
              setCurrentIndex(index)
              onItemChange?.(index)
            }
          }
        })
      },
      {
        root: container,
        threshold: [threshold],
        rootMargin: '0px'
      }
    )

    // Observe all items
    Array.from(items).forEach((item) => {
      observer.observe(item)
    })

    return () => {
      observer.disconnect()
    }
  }, [items.length, threshold, currentIndex, setCurrentIndex, onItemChange])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
      if (autoAdvanceTimeoutRef.current) {
        clearTimeout(autoAdvanceTimeoutRef.current)
      }
    }
  }, [])

  return {
    containerRef,
    currentIndex,
    isScrolling,
    scrollToIndex,
    scrollDirection,
  }
}
