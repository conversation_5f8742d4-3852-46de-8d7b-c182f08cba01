import { useCallback, useEffect } from 'react'
import { useFeedStore } from '@/lib/stores/feed-store'
import { useUserPreferencesStore } from '@/lib/stores/user-preferences-store'
import { FeedType, FeedFilters, GetFeedRequest, UseFeedDataReturn } from '@/types/feed'

export function useFeedData(
  feedType: FeedType,
  initialFilters?: FeedFilters
): UseFeedDataReturn {
  const {
    items,
    loading,
    error,
    hasMore,
    nextCursor,
    filters,
    setCurrentFeed,
    setLoading,
    setError,
    addItems,
    setItems,
    setHasMore,
    setNextCursor,
    updateFilters,
    clearFeed,
  } = useFeedStore()

  const { blockedCreators } = useUserPreferencesStore()

  // Initialize feed type and filters
  useEffect(() => {
    setCurrentFeed(feedType)
    if (initialFilters) {
      updateFilters(initialFilters)
    }
  }, [feedType, initialFilters, setCurrentFeed, updateFilters])

  const fetchFeed = useCallback(async (
    cursor?: string,
    isRefresh = false
  ) => {
    try {
      setLoading(true)
      setError(undefined)

      const request: GetFeedRequest = {
        type: feedType,
        cursor,
        limit: 20,
        filters: {
          ...filters,
          // Always exclude blocked creators
          creatorIds: filters.creatorIds?.filter(id => !blockedCreators.includes(id)),
        },
      }

      const response = await fetch('/api/feed', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch feed: ${response.statusText}`)
      }

      const data = await response.json()

      if (isRefresh) {
        setItems(data.items)
      } else {
        addItems(data.items)
      }

      setHasMore(data.hasMore)
      setNextCursor(data.nextCursor)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load feed'
      setError(errorMessage)
      console.error('Feed fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [
    feedType,
    filters,
    blockedCreators,
    setLoading,
    setError,
    addItems,
    setItems,
    setHasMore,
    setNextCursor,
  ])

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return
    await fetchFeed(nextCursor)
  }, [loading, hasMore, nextCursor, fetchFeed])

  const refresh = useCallback(async () => {
    clearFeed()
    await fetchFeed(undefined, true)
  }, [clearFeed, fetchFeed])

  const updateFiltersAndRefresh = useCallback((newFilters: Partial<FeedFilters>) => {
    updateFilters(newFilters)
    // Refresh will be triggered by the filters change effect
  }, [updateFilters])

  // Load initial data
  useEffect(() => {
    if (items.length === 0 && !loading) {
      fetchFeed(undefined, true)
    }
  }, [feedType, filters, fetchFeed, items.length, loading])

  // Refresh when blocked creators change
  useEffect(() => {
    if (items.length > 0) {
      refresh()
    }
  }, [blockedCreators, refresh])

  return {
    items,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    updateFilters: updateFiltersAndRefresh,
  }
}
