import { useMemo, useCallback } from 'react'
import { useFeedStore, selectFilteredItems } from '@/lib/stores/feed-store'
import { useUserPreferencesStore } from '@/lib/stores/user-preferences-store'
import { MoodType } from '@/types'
import { UseMoodFilterReturn } from '@/types/feed'

export function useMoodFilter(): UseMoodFilterReturn {
  const {
    filters,
    updateFilters,
    items,
  } = useFeedStore()

  const {
    preferredMoods,
    setPreferredMoods,
  } = useUserPreferencesStore()

  // Get current selected moods from filters or user preferences
  const selectedMoods = useMemo(() => {
    return filters.moods || preferredMoods
  }, [filters.moods, preferredMoods])

  // Set selected moods and update filters
  const setSelectedMoods = useCallback((moods: MoodType[]) => {
    updateFilters({ moods })
    // Also update user preferences for persistence
    setPreferredMoods(moods)
  }, [updateFilters, setPreferredMoods])

  // Get filtered items based on current mood selection
  const filteredItems = useMemo(() => {
    if (!selectedMoods.length) {
      return items
    }

    return items.filter(item => {
      return item.post.moods.some(mood => 
        selectedMoods.includes(mood as MoodType)
      )
    })
  }, [items, selectedMoods])

  // Calculate mood counts for all available content
  const moodCounts = useMemo(() => {
    const counts: Record<MoodType, number> = {
      happy: 0,
      chill: 0,
      heartbroken: 0,
      inspired: 0,
      energetic: 0,
      peaceful: 0,
      nostalgic: 0,
      excited: 0,
    }

    items.forEach(item => {
      item.post.moods.forEach(mood => {
        if (mood in counts) {
          counts[mood as MoodType]++
        }
      })
    })

    return counts
  }, [items])

  // Clear all mood filters
  const clearMoods = useCallback(() => {
    setSelectedMoods([])
  }, [setSelectedMoods])

  // Helper functions for mood management
  const addMood = useCallback((mood: MoodType) => {
    if (!selectedMoods.includes(mood)) {
      setSelectedMoods([...selectedMoods, mood])
    }
  }, [selectedMoods, setSelectedMoods])

  const removeMood = useCallback((mood: MoodType) => {
    setSelectedMoods(selectedMoods.filter(m => m !== mood))
  }, [selectedMoods, setSelectedMoods])

  const toggleMood = useCallback((mood: MoodType) => {
    if (selectedMoods.includes(mood)) {
      removeMood(mood)
    } else {
      addMood(mood)
    }
  }, [selectedMoods, addMood, removeMood])

  // Get mood-based recommendations using AI
  const getMoodRecommendations = useCallback(async (targetMood: MoodType) => {
    try {
      const response = await fetch('/api/feed/mood-match', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userMood: targetMood,
          limit: 10,
          excludePostIds: items.map(item => item.post.id),
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get mood recommendations')
      }

      const recommendations = await response.json()
      return recommendations
    } catch (error) {
      console.error('Error getting mood recommendations:', error)
      return []
    }
  }, [items])

  // Get mood distribution for analytics
  const getMoodDistribution = useCallback(() => {
    const total = items.length
    if (total === 0) return {}

    const distribution: Record<MoodType, number> = {} as Record<MoodType, number>
    
    Object.entries(moodCounts).forEach(([mood, count]) => {
      distribution[mood as MoodType] = (count / total) * 100
    })

    return distribution
  }, [items.length, moodCounts])

  // Get trending moods (most popular in current feed)
  const getTrendingMoods = useCallback(() => {
    const sortedMoods = Object.entries(moodCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([mood]) => mood as MoodType)

    return sortedMoods
  }, [moodCounts])

  return {
    selectedMoods,
    setSelectedMoods,
    filteredItems,
    moodCounts,
    clearMoods,
  }
}
