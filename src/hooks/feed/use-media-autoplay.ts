import { useRef, useEffect, useCallback, useState } from 'react'
import { FeedItem } from '@/types/feed'

interface UseMediaAutoplayOptions {
  threshold?: number // Intersection threshold for triggering autoplay
  rootMargin?: string // Root margin for intersection observer
  autoplayDelay?: number // Delay before starting autoplay (ms)
  pauseDelay?: number // Delay before pausing when out of view (ms)
  enableVideoAutoplay?: boolean
  enableAudioAutoplay?: boolean
  muteByDefault?: boolean
}

interface MediaElement {
  element: HTMLVideoElement | HTMLAudioElement
  type: 'video' | 'audio'
  itemId: string
  isPlaying: boolean
  wasPlayingBeforePause: boolean
}

export function useMediaAutoplay(
  items: FeedItem[],
  currentIndex: number,
  options: UseMediaAutoplayOptions = {}
) {
  const {
    threshold = 0.6,
    rootMargin = '0px',
    autoplayDelay = 100,
    pauseDelay = 200,
    enableVideoAutoplay = true,
    enableAudioAutoplay = false, // Audio autoplay is more intrusive
    muteByDefault = true,
  } = options

  const [activeMediaElements, setActiveMediaElements] = useState<Map<string, MediaElement>>(new Map())
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const autoplayTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const pauseTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map())

  // Register a media element for autoplay management
  const registerMediaElement = useCallback((
    element: HTMLVideoElement | HTMLAudioElement,
    itemId: string,
    type: 'video' | 'audio'
  ) => {
    // Set default properties
    if (muteByDefault) {
      element.muted = true
    }
    
    // Add event listeners
    const handlePlay = () => {
      setActiveMediaElements(prev => {
        const newMap = new Map(prev)
        const mediaElement = newMap.get(itemId)
        if (mediaElement) {
          newMap.set(itemId, { ...mediaElement, isPlaying: true })
        }
        return newMap
      })
      setCurrentlyPlaying(itemId)
    }

    const handlePause = () => {
      setActiveMediaElements(prev => {
        const newMap = new Map(prev)
        const mediaElement = newMap.get(itemId)
        if (mediaElement) {
          newMap.set(itemId, { ...mediaElement, isPlaying: false })
        }
        return newMap
      })
      if (currentlyPlaying === itemId) {
        setCurrentlyPlaying(null)
      }
    }

    const handleEnded = () => {
      setActiveMediaElements(prev => {
        const newMap = new Map(prev)
        const mediaElement = newMap.get(itemId)
        if (mediaElement) {
          newMap.set(itemId, { ...mediaElement, isPlaying: false })
        }
        return newMap
      })
      if (currentlyPlaying === itemId) {
        setCurrentlyPlaying(null)
      }
    }

    element.addEventListener('play', handlePlay)
    element.addEventListener('pause', handlePause)
    element.addEventListener('ended', handleEnded)

    // Register the element
    setActiveMediaElements(prev => {
      const newMap = new Map(prev)
      newMap.set(itemId, {
        element,
        type,
        itemId,
        isPlaying: false,
        wasPlayingBeforePause: false,
      })
      return newMap
    })

    // Return cleanup function
    return () => {
      element.removeEventListener('play', handlePlay)
      element.removeEventListener('pause', handlePause)
      element.removeEventListener('ended', handleEnded)
      
      setActiveMediaElements(prev => {
        const newMap = new Map(prev)
        newMap.delete(itemId)
        return newMap
      })
    }
  }, [muteByDefault, currentlyPlaying])

  // Unregister a media element
  const unregisterMediaElement = useCallback((itemId: string) => {
    // Clear any pending timeouts
    const autoplayTimeout = autoplayTimeoutsRef.current.get(itemId)
    if (autoplayTimeout) {
      clearTimeout(autoplayTimeout)
      autoplayTimeoutsRef.current.delete(itemId)
    }

    const pauseTimeout = pauseTimeoutsRef.current.get(itemId)
    if (pauseTimeout) {
      clearTimeout(pauseTimeout)
      pauseTimeoutsRef.current.delete(itemId)
    }

    setActiveMediaElements(prev => {
      const newMap = new Map(prev)
      newMap.delete(itemId)
      return newMap
    })
  }, [])

  // Play media element with error handling
  const playMediaElement = useCallback(async (itemId: string) => {
    const mediaElement = activeMediaElements.get(itemId)
    if (!mediaElement || mediaElement.isPlaying) return

    try {
      // Pause other playing media first (only one should play at a time)
      activeMediaElements.forEach((otherMedia, otherId) => {
        if (otherId !== itemId && otherMedia.isPlaying) {
          otherMedia.element.pause()
        }
      })

      await mediaElement.element.play()
    } catch (error) {
      console.warn(`Failed to play media for item ${itemId}:`, error)
      
      // If autoplay failed due to user interaction requirements, 
      // we'll wait for user interaction
      if (error instanceof DOMException && error.name === 'NotAllowedError') {
        console.log('Autoplay blocked - waiting for user interaction')
      }
    }
  }, [activeMediaElements])

  // Pause media element
  const pauseMediaElement = useCallback((itemId: string, rememberState = true) => {
    const mediaElement = activeMediaElements.get(itemId)
    if (!mediaElement) return

    if (rememberState) {
      setActiveMediaElements(prev => {
        const newMap = new Map(prev)
        const element = newMap.get(itemId)
        if (element) {
          newMap.set(itemId, { 
            ...element, 
            wasPlayingBeforePause: element.isPlaying 
          })
        }
        return newMap
      })
    }

    if (!mediaElement.element.paused) {
      mediaElement.element.pause()
    }
  }, [activeMediaElements])

  // Handle intersection changes
  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    entries.forEach((entry) => {
      const itemId = entry.target.getAttribute('data-post-id')
      if (!itemId) return

      const mediaElement = activeMediaElements.get(itemId)
      if (!mediaElement) return

      const shouldAutoplay = (
        (mediaElement.type === 'video' && enableVideoAutoplay) ||
        (mediaElement.type === 'audio' && enableAudioAutoplay)
      )

      if (entry.isIntersecting && entry.intersectionRatio >= threshold) {
        // Clear any pending pause timeout
        const pauseTimeout = pauseTimeoutsRef.current.get(itemId)
        if (pauseTimeout) {
          clearTimeout(pauseTimeout)
          pauseTimeoutsRef.current.delete(itemId)
        }

        // Schedule autoplay if enabled
        if (shouldAutoplay) {
          const autoplayTimeout = setTimeout(() => {
            playMediaElement(itemId)
            autoplayTimeoutsRef.current.delete(itemId)
          }, autoplayDelay)
          
          autoplayTimeoutsRef.current.set(itemId, autoplayTimeout)
        }
      } else {
        // Clear any pending autoplay timeout
        const autoplayTimeout = autoplayTimeoutsRef.current.get(itemId)
        if (autoplayTimeout) {
          clearTimeout(autoplayTimeout)
          autoplayTimeoutsRef.current.delete(itemId)
        }

        // Schedule pause
        const pauseTimeout = setTimeout(() => {
          pauseMediaElement(itemId)
          pauseTimeoutsRef.current.delete(itemId)
        }, pauseDelay)
        
        pauseTimeoutsRef.current.set(itemId, pauseTimeout)
      }
    })
  }, [
    activeMediaElements,
    threshold,
    enableVideoAutoplay,
    enableAudioAutoplay,
    autoplayDelay,
    pauseDelay,
    playMediaElement,
    pauseMediaElement,
  ])

  // Set up intersection observer
  useEffect(() => {
    observerRef.current = new IntersectionObserver(handleIntersection, {
      threshold: [0, threshold, 1],
      rootMargin,
    })

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [handleIntersection, threshold, rootMargin])

  // Observe content cards
  const observeElement = useCallback((element: HTMLElement) => {
    if (observerRef.current) {
      observerRef.current.observe(element)
    }
  }, [])

  // Unobserve content cards
  const unobserveElement = useCallback((element: HTMLElement) => {
    if (observerRef.current) {
      observerRef.current.unobserve(element)
    }
  }, [])

  // Manual play/pause controls
  const playItem = useCallback((itemId: string) => {
    playMediaElement(itemId)
  }, [playMediaElement])

  const pauseItem = useCallback((itemId: string) => {
    pauseMediaElement(itemId, false)
  }, [pauseMediaElement])

  const toggleItem = useCallback((itemId: string) => {
    const mediaElement = activeMediaElements.get(itemId)
    if (!mediaElement) return

    if (mediaElement.isPlaying) {
      pauseItem(itemId)
    } else {
      playItem(itemId)
    }
  }, [activeMediaElements, playItem, pauseItem])

  // Mute/unmute controls
  const muteItem = useCallback((itemId: string) => {
    const mediaElement = activeMediaElements.get(itemId)
    if (mediaElement) {
      mediaElement.element.muted = true
    }
  }, [activeMediaElements])

  const unmuteItem = useCallback((itemId: string) => {
    const mediaElement = activeMediaElements.get(itemId)
    if (mediaElement) {
      mediaElement.element.muted = false
    }
  }, [activeMediaElements])

  const toggleMute = useCallback((itemId: string) => {
    const mediaElement = activeMediaElements.get(itemId)
    if (mediaElement) {
      mediaElement.element.muted = !mediaElement.element.muted
    }
  }, [activeMediaElements])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all timeouts
      autoplayTimeoutsRef.current.forEach((timeout) => clearTimeout(timeout))
      pauseTimeoutsRef.current.forEach((timeout) => clearTimeout(timeout))
      autoplayTimeoutsRef.current.clear()
      pauseTimeoutsRef.current.clear()

      // Pause all playing media
      activeMediaElements.forEach((mediaElement) => {
        if (!mediaElement.element.paused) {
          mediaElement.element.pause()
        }
      })
    }
  }, [activeMediaElements])

  return {
    registerMediaElement,
    unregisterMediaElement,
    observeElement,
    unobserveElement,
    playItem,
    pauseItem,
    toggleItem,
    muteItem,
    unmuteItem,
    toggleMute,
    currentlyPlaying,
    activeMediaElements,
  }
}
