import { useCallback, useEffect, useState } from 'react'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { MediaSource } from '@/lib/player/types'

interface PlaylistItem {
  id: string
  source: MediaSource
  metadata?: any
  duration?: number
}

interface Playlist {
  id: string
  name: string
  items: PlaylistItem[]
  currentIndex: number
  shuffle: boolean
  repeat: 'none' | 'one' | 'all'
}

interface UsePlaylistReturn {
  // Current playlist state
  playlist: Playlist | null
  currentItem: PlaylistItem | null
  currentIndex: number
  isPlaying: boolean
  canGoNext: boolean
  canGoPrevious: boolean
  
  // Playlist management
  createPlaylist: (name: string, items?: PlaylistItem[]) => string
  deletePlaylist: (id: string) => void
  loadPlaylist: (id: string) => void
  
  // Item management
  addItem: (item: PlaylistItem) => void
  removeItem: (id: string) => void
  reorderItems: (fromIndex: number, toIndex: number) => void
  
  // Playback control
  play: (index?: number) => Promise<void>
  next: () => Promise<void>
  previous: () => Promise<void>
  goToIndex: (index: number) => Promise<void>
  
  // Playlist modes
  toggleShuffle: () => void
  setRepeat: (mode: 'none' | 'one' | 'all') => void
  
  // Queue management
  addToQueue: (item: PlaylistItem) => void
  queue: PlaylistItem[]
  clearQueue: () => void
}

export function usePlaylist(playlistId?: string): UsePlaylistReturn {
  const {
    playlists,
    currentPlaylist,
    queue,
    isPlaying,
    createPlaylist: storeCreatePlaylist,
    deletePlaylist: storeDeletePlaylist,
    addToPlaylist,
    removeFromPlaylist,
    reorderPlaylist,
    setCurrentPlaylist,
    playPlaylist,
    playNext: storePlayNext,
    playPrevious: storePrevious,
    addToQueue,
    clearQueue
  } = usePlayerStore()

  const [localPlaylistId, setLocalPlaylistId] = useState<string | null>(playlistId || null)
  
  // Get current playlist
  const playlist = localPlaylistId ? playlists.get(localPlaylistId) : null
  const currentItem = playlist?.items[playlist.currentIndex] || null
  
  // Load playlist by ID
  const loadPlaylist = useCallback((id: string) => {
    setLocalPlaylistId(id)
    setCurrentPlaylist(id)
  }, [setCurrentPlaylist])

  // Create new playlist
  const createPlaylist = useCallback((name: string, items: PlaylistItem[] = []) => {
    const id = storeCreatePlaylist(name, items)
    setLocalPlaylistId(id)
    return id
  }, [storeCreatePlaylist])

  // Delete playlist
  const deletePlaylist = useCallback((id: string) => {
    storeDeletePlaylist(id)
    if (localPlaylistId === id) {
      setLocalPlaylistId(null)
    }
  }, [storeDeletePlaylist, localPlaylistId])

  // Add item to current playlist
  const addItem = useCallback((item: PlaylistItem) => {
    if (localPlaylistId) {
      addToPlaylist(localPlaylistId, item)
    }
  }, [localPlaylistId, addToPlaylist])

  // Remove item from current playlist
  const removeItem = useCallback((id: string) => {
    if (localPlaylistId) {
      removeFromPlaylist(localPlaylistId, id)
    }
  }, [localPlaylistId, removeFromPlaylist])

  // Reorder items in current playlist
  const reorderItems = useCallback((fromIndex: number, toIndex: number) => {
    if (localPlaylistId) {
      reorderPlaylist(localPlaylistId, fromIndex, toIndex)
    }
  }, [localPlaylistId, reorderPlaylist])

  // Play playlist from specific index
  const play = useCallback(async (index: number = 0) => {
    if (localPlaylistId) {
      await playPlaylist(localPlaylistId, index)
    }
  }, [localPlaylistId, playPlaylist])

  // Go to specific index
  const goToIndex = useCallback(async (index: number) => {
    await play(index)
  }, [play])

  // Play next track
  const next = useCallback(async () => {
    await storePlayNext()
  }, [storePlayNext])

  // Play previous track
  const previous = useCallback(async () => {
    await storePrevious()
  }, [storePrevious])

  // Toggle shuffle mode
  const toggleShuffle = useCallback(() => {
    if (localPlaylistId && playlist) {
      const newPlaylists = new Map(playlists)
      const updatedPlaylist = { ...playlist, shuffle: !playlist.shuffle }
      newPlaylists.set(localPlaylistId, updatedPlaylist)
      // Note: This would need to be implemented in the store
    }
  }, [localPlaylistId, playlist, playlists])

  // Set repeat mode
  const setRepeat = useCallback((mode: 'none' | 'one' | 'all') => {
    if (localPlaylistId && playlist) {
      const newPlaylists = new Map(playlists)
      const updatedPlaylist = { ...playlist, repeat: mode }
      newPlaylists.set(localPlaylistId, updatedPlaylist)
      // Note: This would need to be implemented in the store
    }
  }, [localPlaylistId, playlist, playlists])

  // Calculate navigation capabilities
  const canGoNext = playlist ? 
    (playlist.currentIndex < playlist.items.length - 1) || 
    playlist.repeat === 'all' || 
    queue.length > 0 : false

  const canGoPrevious = playlist ? 
    (playlist.currentIndex > 0) || 
    playlist.repeat === 'all' : false

  return {
    playlist,
    currentItem,
    currentIndex: playlist?.currentIndex || 0,
    isPlaying,
    canGoNext,
    canGoPrevious,
    createPlaylist,
    deletePlaylist,
    loadPlaylist,
    addItem,
    removeItem,
    reorderItems,
    play,
    next,
    previous,
    goToIndex,
    toggleShuffle,
    setRepeat,
    addToQueue,
    queue,
    clearQueue
  }
}

// Hook for creating and managing HVPPY content playlists
export function useHVPPYPlaylist(contentIds?: string[]) {
  const playlistHook = usePlaylist()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load HVPPY content into playlist
  const loadHVPPYContent = useCallback(async (ids: string[]) => {
    setLoading(true)
    setError(null)

    try {
      const items: PlaylistItem[] = []
      
      for (const id of ids) {
        const response = await fetch(`/api/content/${id}`)
        if (!response.ok) throw new Error(`Failed to load content ${id}`)
        
        const content = await response.json()
        
        if (content.source) {
          items.push({
            id,
            source: content.source,
            metadata: content,
            duration: content.duration
          })
        }
      }

      // Create playlist with loaded content
      const playlistId = playlistHook.createPlaylist('HVPPY Playlist', items)
      playlistHook.loadPlaylist(playlistId)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [playlistHook])

  // Load content on mount if provided
  useEffect(() => {
    if (contentIds && contentIds.length > 0) {
      loadHVPPYContent(contentIds)
    }
  }, [contentIds, loadHVPPYContent])

  return {
    ...playlistHook,
    loadHVPPYContent,
    loading,
    error
  }
}

// Hook for mood-based playlists
export function useMoodPlaylist(mood?: string) {
  const playlistHook = usePlaylist()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadMoodPlaylist = useCallback(async (selectedMood: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/content/mood/${selectedMood}`)
      if (!response.ok) throw new Error('Failed to load mood content')
      
      const content = await response.json()
      
      const items: PlaylistItem[] = content.map((item: any) => ({
        id: item.id,
        source: item.source,
        metadata: item,
        duration: item.duration
      }))

      const playlistId = playlistHook.createPlaylist(`${selectedMood} Mood`, items)
      playlistHook.loadPlaylist(playlistId)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [playlistHook])

  useEffect(() => {
    if (mood) {
      loadMoodPlaylist(mood)
    }
  }, [mood, loadMoodPlaylist])

  return {
    ...playlistHook,
    loadMoodPlaylist,
    loading,
    error
  }
}

// Hook for auto-generated playlists based on listening history
export function useSmartPlaylist(userId?: string) {
  const playlistHook = usePlaylist()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const generateSmartPlaylist = useCallback(async (type: 'recent' | 'favorites' | 'recommended') => {
    if (!userId) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/playlists/smart/${type}?userId=${userId}`)
      if (!response.ok) throw new Error('Failed to generate smart playlist')
      
      const data = await response.json()
      
      const items: PlaylistItem[] = data.items.map((item: any) => ({
        id: item.id,
        source: item.source,
        metadata: item,
        duration: item.duration
      }))

      const playlistId = playlistHook.createPlaylist(data.name, items)
      playlistHook.loadPlaylist(playlistId)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [userId, playlistHook])

  return {
    ...playlistHook,
    generateSmartPlaylist,
    loading,
    error
  }
}
