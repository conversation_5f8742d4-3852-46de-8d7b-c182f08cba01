import { useEffect, useCallback, useRef, useState } from 'react'
import { VideoPlayer, MediaSource, PlayerConfig, UseVideoPlayerReturn } from '@/lib/player/types'
import { createVideoPlayer } from '@/lib/player'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'

interface UseVideoPlayerOptions {
  element?: HTMLVideoElement
  source?: MediaSource
  config?: Partial<PlayerConfig>
  autoPlay?: boolean
  onReady?: (player: VideoPlayer) => void
  onStateChange?: (state: any) => void
  onError?: (error: any) => void
}

export function useVideoPlayer(options: UseVideoPlayerOptions = {}): UseVideoPlayerReturn {
  const {
    element,
    source,
    config = {},
    autoPlay = false,
    onReady,
    onStateChange,
    onError
  } = options

  const [player, setPlayer] = useState<VideoPlayer | null>(null)
  const [state, setState] = useState<any>({
    state: 'idle',
    currentTime: 0,
    duration: 0,
    bufferedTime: 0,
    volume: 0.8,
    muted: false,
    playbackRate: 1,
    quality: 'auto',
    isFullscreen: false,
    isPiP: false,
    isLive: false
  })

  const playerRef = useRef<VideoPlayer | null>(null)
  const {
    registerPlayer,
    unregisterPlayer,
    setCurrentPlayer,
    pauseAllExcept
  } = usePlayerStore()

  // Initialize player
  useEffect(() => {
    if (!element) return

    const playerConfig: Partial<PlayerConfig> = {
      autoPlay,
      controls: false,
      ...config
    }

    try {
      const videoPlayer = createVideoPlayer(element, playerConfig)
      setPlayer(videoPlayer)
      playerRef.current = videoPlayer

      // Register with global store
      const playerId = `video-${Date.now()}`
      registerPlayer(playerId, videoPlayer, 'video')

      // Setup event listeners
      videoPlayer.on('onStateChange', (newState) => {
        setState(newState)
        onStateChange?.(newState)
      })

      videoPlayer.on('onError', (error) => {
        onError?.(error)
      })

      onReady?.(videoPlayer)

      return () => {
        unregisterPlayer(playerId)
        videoPlayer.destroy()
      }
    } catch (error) {
      console.error('Failed to initialize video player:', error)
      onError?.(error)
    }
  }, [element, autoPlay, config, onReady, onStateChange, onError, registerPlayer, unregisterPlayer])

  // Load source
  useEffect(() => {
    if (player && source) {
      player.load(source).catch(onError)
    }
  }, [player, source, onError])

  // Player control methods
  const load = useCallback(async (newSource: MediaSource) => {
    if (!player) throw new Error('Player not initialized')
    await player.load(newSource)
  }, [player])

  const play = useCallback(async () => {
    if (!player) return
    
    // Pause other players
    pauseAllExcept(playerRef.current)
    setCurrentPlayer(playerRef.current)
    
    await player.play()
  }, [player, pauseAllExcept, setCurrentPlayer])

  const pause = useCallback(() => {
    if (!player) return
    player.pause()
  }, [player])

  const seek = useCallback((time: number) => {
    if (!player) return
    player.seek(time)
  }, [player])

  const setVolume = useCallback((volume: number) => {
    if (!player) return
    player.setVolume(volume)
  }, [player])

  const setMuted = useCallback((muted: boolean) => {
    if (!player) return
    player.setMuted(muted)
  }, [player])

  const setPlaybackRate = useCallback((rate: number) => {
    if (!player) return
    player.setPlaybackRate(rate)
  }, [player])

  const enterFullscreen = useCallback(async () => {
    if (!player) return
    await player.enterFullscreen()
  }, [player])

  const exitFullscreen = useCallback(async () => {
    if (!player) return
    await player.exitFullscreen()
  }, [player])

  const enterPiP = useCallback(async () => {
    if (!player) return
    await player.enterPiP()
  }, [player])

  const exitPiP = useCallback(async () => {
    if (!player) return
    await player.exitPiP()
  }, [player])

  return {
    player,
    state,
    load,
    play,
    pause,
    seek,
    setVolume,
    setMuted,
    setPlaybackRate,
    enterFullscreen,
    exitFullscreen,
    enterPiP,
    exitPiP
  }
}

// Hook for video player with HVPPY content integration
export function useHVPPYVideoPlayer(contentId?: string) {
  const [source, setSource] = useState<MediaSource | undefined>()
  const [metadata, setMetadata] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load HVPPY content
  useEffect(() => {
    if (!contentId) return

    const loadContent = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/content/${contentId}`)
        if (!response.ok) throw new Error('Failed to load content')

        const content = await response.json()
        setMetadata(content)

        if (content.source) {
          setSource(content.source)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    loadContent()
  }, [contentId])

  const playerHook = useVideoPlayer({
    source,
    config: {
      autoPlay: false,
      muted: true // Start muted for better UX
    }
  })

  return {
    ...playerHook,
    metadata,
    loading,
    error,
    contentId
  }
}

// Hook for vertical video player (TikTok-style)
export function useVerticalVideoPlayer(options: UseVideoPlayerOptions = {}) {
  return useVideoPlayer({
    ...options,
    config: {
      autoPlay: true,
      muted: true,
      loop: false,
      ...options.config
    }
  })
}

// Hook for video player with keyboard controls
export function useVideoPlayerWithKeyboard(options: UseVideoPlayerOptions = {}) {
  const playerHook = useVideoPlayer(options)
  const { player } = playerHook

  useEffect(() => {
    if (!player) return

    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle if no input is focused
      if (document.activeElement?.tagName === 'INPUT' || 
          document.activeElement?.tagName === 'TEXTAREA') {
        return
      }

      const handled = (player as any).handleKeyboard?.(event)
      if (handled) {
        event.preventDefault()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [player])

  return playerHook
}

// Hook for video player with auto-pause on visibility change
export function useVideoPlayerWithVisibility(options: UseVideoPlayerOptions = {}) {
  const playerHook = useVideoPlayerWithKeyboard(options)
  const { player, state } = playerHook
  const wasPlayingRef = useRef(false)

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!player) return

      if (document.hidden) {
        // Page is hidden, pause if playing
        if (state.state === 'playing') {
          wasPlayingRef.current = true
          player.pause()
        }
      } else {
        // Page is visible, resume if was playing
        if (wasPlayingRef.current) {
          player.play().catch(console.error)
          wasPlayingRef.current = false
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [player, state.state])

  return playerHook
}

// Hook for video player with intersection observer (auto-play when in view)
export function useVideoPlayerWithIntersection(
  elementRef: React.RefObject<HTMLElement>,
  options: UseVideoPlayerOptions & { threshold?: number } = {}
) {
  const { threshold = 0.5, ...playerOptions } = options
  const playerHook = useVideoPlayerWithVisibility(playerOptions)
  const { player } = playerHook
  const [isInView, setIsInView] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element || !player) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting)
        
        if (entry.isIntersecting && playerOptions.autoPlay !== false) {
          player.play().catch(console.error)
        } else {
          player.pause()
        }
      },
      { threshold }
    )

    observer.observe(element)
    return () => observer.disconnect()
  }, [elementRef, player, threshold, playerOptions.autoPlay])

  return {
    ...playerHook,
    isInView
  }
}
