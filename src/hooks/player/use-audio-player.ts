import { useEffect, useCallback, useRef, useState } from 'react'
import { AudioPlayer, MediaSource, PlayerConfig, UseAudioPlayerReturn } from '@/lib/player/types'
import { createAudioPlayer } from '@/lib/player'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'

interface UseAudioPlayerOptions {
  element?: HTMLAudioElement
  source?: MediaSource
  config?: Partial<PlayerConfig>
  autoPlay?: boolean
  enableVisualizer?: boolean
  onReady?: (player: AudioPlayer) => void
  onStateChange?: (state: any) => void
  onError?: (error: any) => void
}

export function useAudioPlayer(options: UseAudioPlayerOptions = {}): UseAudioPlayerReturn {
  const {
    element,
    source,
    config = {},
    autoPlay = false,
    enableVisualizer = true,
    onReady,
    onStateChange,
    onError
  } = options

  const [player, setPlayer] = useState<AudioPlayer | null>(null)
  const [state, setState] = useState<any>({
    state: 'idle',
    currentTime: 0,
    duration: 0,
    bufferedTime: 0,
    volume: 0.8,
    muted: false,
    playbackRate: 1,
    quality: 'medium',
    isFullscreen: false,
    isPiP: false,
    isLive: false
  })
  const [frequencyData, setFrequencyData] = useState<Uint8Array | null>(null)
  const [waveformData, setWaveformData] = useState<Uint8Array | null>(null)

  const playerRef = useRef<AudioPlayer | null>(null)
  const animationFrameRef = useRef<number>()
  const {
    registerPlayer,
    unregisterPlayer,
    setCurrentPlayer,
    pauseAllExcept
  } = usePlayerStore()

  // Initialize player
  useEffect(() => {
    if (!element) return

    const playerConfig: Partial<PlayerConfig> = {
      autoPlay,
      controls: false,
      ...config
    }

    try {
      const audioPlayer = createAudioPlayer(element, playerConfig)
      setPlayer(audioPlayer)
      playerRef.current = audioPlayer

      // Register with global store
      const playerId = `audio-${Date.now()}`
      registerPlayer(playerId, audioPlayer, 'audio')

      // Setup event listeners
      audioPlayer.on('onStateChange', (newState) => {
        setState(newState)
        onStateChange?.(newState)
      })

      audioPlayer.on('onError', (error) => {
        onError?.(error)
      })

      // Resume audio context if needed
      audioPlayer.resumeAudioContext?.().catch(console.warn)

      onReady?.(audioPlayer)

      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current)
        }
        unregisterPlayer(playerId)
        audioPlayer.destroy()
      }
    } catch (error) {
      console.error('Failed to initialize audio player:', error)
      onError?.(error)
    }
  }, [element, autoPlay, config, onReady, onStateChange, onError, registerPlayer, unregisterPlayer])

  // Update visualizer data
  useEffect(() => {
    if (!player || !enableVisualizer) return

    const updateVisualizerData = () => {
      const freqData = player.getFrequencyData()
      const waveData = player.getWaveformData()
      
      setFrequencyData(freqData)
      setWaveformData(waveData)
      
      animationFrameRef.current = requestAnimationFrame(updateVisualizerData)
    }

    if (state.state === 'playing') {
      updateVisualizerData()
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [player, enableVisualizer, state.state])

  // Load source
  useEffect(() => {
    if (player && source) {
      player.load(source).catch(onError)
    }
  }, [player, source, onError])

  // Player control methods
  const load = useCallback(async (newSource: MediaSource) => {
    if (!player) throw new Error('Player not initialized')
    await player.load(newSource)
  }, [player])

  const play = useCallback(async () => {
    if (!player) return
    
    // Resume audio context if suspended
    await player.resumeAudioContext?.()
    
    // Pause other players
    pauseAllExcept(playerRef.current)
    setCurrentPlayer(playerRef.current)
    
    await player.play()
  }, [player, pauseAllExcept, setCurrentPlayer])

  const pause = useCallback(() => {
    if (!player) return
    player.pause()
  }, [player])

  const seek = useCallback((time: number) => {
    if (!player) return
    player.seek(time)
  }, [player])

  const setVolume = useCallback((volume: number) => {
    if (!player) return
    player.setVolume(volume)
  }, [player])

  const setMuted = useCallback((muted: boolean) => {
    if (!player) return
    player.setMuted(muted)
  }, [player])

  const setPlaybackRate = useCallback((rate: number) => {
    if (!player) return
    player.setPlaybackRate(rate)
  }, [player])

  const setEqualizer = useCallback((bands: number[]) => {
    if (!player) return
    player.setEqualizer(bands)
  }, [player])

  return {
    player,
    state,
    frequencyData,
    waveformData,
    load,
    play,
    pause,
    seek,
    setVolume,
    setMuted,
    setPlaybackRate,
    setEqualizer
  }
}

// Hook for audio player with HVPPY content integration
export function useHVPPYAudioPlayer(contentId?: string) {
  const [source, setSource] = useState<MediaSource | undefined>()
  const [metadata, setMetadata] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load HVPPY content
  useEffect(() => {
    if (!contentId) return

    const loadContent = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/content/${contentId}`)
        if (!response.ok) throw new Error('Failed to load content')

        const content = await response.json()
        setMetadata(content)

        if (content.source) {
          setSource(content.source)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    loadContent()
  }, [contentId])

  const playerHook = useAudioPlayer({
    source,
    config: {
      autoPlay: false
    }
  })

  return {
    ...playerHook,
    metadata,
    loading,
    error,
    contentId
  }
}

// Hook for audio player with mood-based EQ
export function useAudioPlayerWithMoodEQ(mood?: string, options: UseAudioPlayerOptions = {}) {
  const playerHook = useAudioPlayer(options)
  const { player, setEqualizer } = playerHook

  useEffect(() => {
    if (!player || !mood) return

    // Apply mood-based equalizer settings
    const moodEQSettings: Record<string, number[]> = {
      energetic: [6, 4, 1, 3, -1, -1, 1, 2, 3, 4], // Hip-hop style
      peaceful: [5, 4, 3, 2, -1, -2, -1, 2, 3, 4], // Classical style
      confident: [5, 3, -1, -2, 1, 2, 4, 5, 5, 5], // Rock style
      emotional: [2, 1, -1, -2, -1, 2, 4, 4, 3, 1], // Vocal style
      inspired: [2, 1, 0, -1, -1, 0, 1, 2, 3, 3], // Pop style
      healing: [4, 3, 1, 2, -1, -1, 0, 1, 2, 3] // Jazz style
    }

    const eqSettings = moodEQSettings[mood]
    if (eqSettings) {
      setEqualizer(eqSettings)
    }
  }, [player, mood, setEqualizer])

  return playerHook
}

// Hook for audio player with auto-pause on phone calls (mobile)
export function useAudioPlayerWithCallDetection(options: UseAudioPlayerOptions = {}) {
  const playerHook = useAudioPlayer(options)
  const { player, state } = playerHook
  const wasPlayingRef = useRef(false)

  useEffect(() => {
    if (typeof window === 'undefined' || !('navigator' in window)) return

    const handleAudioInterruption = () => {
      if (state.state === 'playing') {
        wasPlayingRef.current = true
        player?.pause()
      }
    }

    const handleAudioResume = () => {
      if (wasPlayingRef.current) {
        player?.play().catch(console.error)
        wasPlayingRef.current = false
      }
    }

    // Listen for audio interruptions (iOS Safari)
    document.addEventListener('webkitbeginfullscreen', handleAudioInterruption)
    document.addEventListener('webkitendfullscreen', handleAudioResume)

    // Listen for page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        handleAudioInterruption()
      } else {
        handleAudioResume()
      }
    })

    return () => {
      document.removeEventListener('webkitbeginfullscreen', handleAudioInterruption)
      document.removeEventListener('webkitendfullscreen', handleAudioResume)
    }
  }, [player, state.state])

  return playerHook
}

// Hook for audio player with crossfade support
export function useAudioPlayerWithCrossfade(
  crossfadeDuration: number = 3000,
  options: UseAudioPlayerOptions = {}
) {
  const playerHook = useAudioPlayer(options)
  const { player, state } = playerHook
  const crossfadeTimeoutRef = useRef<NodeJS.Timeout>()

  const playWithCrossfade = useCallback(async (newSource: MediaSource) => {
    if (!player) return

    const currentVolume = state.volume

    // Fade out current track
    if (state.state === 'playing') {
      const fadeOutSteps = 20
      const fadeOutInterval = crossfadeDuration / 2 / fadeOutSteps

      for (let i = fadeOutSteps; i >= 0; i--) {
        const volume = (i / fadeOutSteps) * currentVolume
        player.setVolume(volume)
        await new Promise(resolve => setTimeout(resolve, fadeOutInterval))
      }
    }

    // Load and play new track
    await player.load(newSource)
    await player.play()

    // Fade in new track
    const fadeInSteps = 20
    const fadeInInterval = crossfadeDuration / 2 / fadeInSteps

    for (let i = 0; i <= fadeInSteps; i++) {
      const volume = (i / fadeInSteps) * currentVolume
      player.setVolume(volume)
      await new Promise(resolve => setTimeout(resolve, fadeInInterval))
    }
  }, [player, state.volume, state.state, crossfadeDuration])

  return {
    ...playerHook,
    playWithCrossfade
  }
}

// Hook for audio player with gapless playback
export function useAudioPlayerWithGapless(options: UseAudioPlayerOptions = {}) {
  const playerHook = useAudioPlayer(options)
  const { player } = playerHook
  const nextSourceRef = useRef<MediaSource | null>(null)
  const preloadPlayerRef = useRef<AudioPlayer | null>(null)

  const preloadNext = useCallback(async (nextSource: MediaSource) => {
    if (!nextSource) return

    nextSourceRef.current = nextSource

    // Create a hidden audio element for preloading
    const preloadElement = document.createElement('audio')
    preloadElement.style.display = 'none'
    document.body.appendChild(preloadElement)

    try {
      const preloadPlayer = createAudioPlayer(preloadElement)
      preloadPlayerRef.current = preloadPlayer
      await preloadPlayer.load(nextSource)
    } catch (error) {
      console.error('Failed to preload next track:', error)
    }
  }, [])

  const playNext = useCallback(async () => {
    if (!nextSourceRef.current || !preloadPlayerRef.current || !player) return

    // Swap players for gapless transition
    const currentElement = player.getAudioElement()
    const preloadElement = preloadPlayerRef.current.getAudioElement()

    // Quick swap
    currentElement.pause()
    await preloadPlayerRef.current.play()

    // Clean up
    preloadPlayerRef.current = null
    nextSourceRef.current = null
  }, [player])

  return {
    ...playerHook,
    preloadNext,
    playNext
  }
}
