"use client"

import React, { useEffect, useCallback, useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import { VerticalFeedContainerProps, InteractionData } from '@/types/feed'
import { useFeedData } from '@/hooks/feed/use-feed-data'
import { useInfiniteScroll } from '@/hooks/feed/use-infinite-scroll'
import { useContentInteractions } from '@/hooks/feed/use-content-interactions'
import { useContentPreloader } from '@/hooks/feed/use-content-preloader'
import { useMediaAutoplay } from '@/hooks/feed/use-media-autoplay'
import { EnhancedContentCard } from './enhanced-content-card'
// import { FeedNavigation } from './feed-navigation'
// import { MoodSelector } from './mood-selector'
import { useMoodFilter } from '@/hooks/feed/use-mood-filter'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { Loader2, RefreshCw, AlertCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'

export function VerticalFeedContainer({
  feedType,
  filters,
  onItemChange,
  onInteraction,
  className,
  autoPlay = true,
}: VerticalFeedContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)
  const [performanceMetrics, setPerformanceMetrics] = useState({
    fps: 60,
    memoryUsage: 0,
    loadedItems: 0,
  })

  const {
    items,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    updateFilters,
  } = useFeedData(feedType, filters)

  const {
    selectedMoods,
    setSelectedMoods,
    moodCounts,
    clearMoods,
  } = useMoodFilter()

  const {
    react,
    unreact,
    toggleMemory,
    share,
  } = useContentInteractions()

  // Initialize performance hooks
  const infiniteScrollHook = useInfiniteScroll({
    threshold: 0.6,
    onItemChange: (index) => {
      setCurrentIndex(index)
      onItemChange?.(items[index], index)
    },
    preloadDistance: 3,
    offloadDistance: 10,
    enableVirtualization: true,
  })

  // Content preloader
  const {
    preloadedContent,
    getPreloadedContent,
    isPreloading,
    preloadedCount,
  } = useContentPreloader(items, currentIndex, {
    preloadDistance: 3,
    offloadDistance: 10,
    enableImagePreload: true,
    enableVideoPreload: true,
    enableAudioPreload: false, // Audio preload can be expensive
  })

  // Media autoplay management
  const {
    registerMediaElement,
    unregisterMediaElement,
    observeElement,
  } = useMediaAutoplay(items, currentIndex, {
    threshold: 0.6,
    autoplayDelay: 200,
    pauseDelay: 300,
    enableVideoAutoplay: autoPlay,
    enableAudioAutoplay: false,
    muteByDefault: true,
  })

  const { pauseAll } = usePlayerStore()

  // Handle scroll to detect current item
  const handleScroll = useCallback(() => {
    if (!containerRef.current || isScrolling) return

    const container = containerRef.current
    const scrollTop = container.scrollTop
    const itemHeight = container.clientHeight
    const newIndex = Math.round(scrollTop / itemHeight)

    if (newIndex !== currentIndex && newIndex >= 0 && newIndex < items.length) {
      setCurrentIndex(newIndex)
      const item = items[newIndex]
      if (item) {
        onItemChange?.(item, newIndex)

        // Load more items when approaching the end
        if (newIndex >= items.length - 3 && hasMore && !loading) {
          loadMore()
        }
      }
    }
  }, [currentIndex, items, onItemChange, hasMore, loading, loadMore, isScrolling])



  // Setup scroll listener
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    return () => container.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  // Pause all players when scrolling starts
  useEffect(() => {
    if (isScrolling) {
      pauseAll()
    }
  }, [isScrolling, pauseAll])

  // Handle interactions
  const handleInteraction = useCallback((interaction: InteractionData) => {
    onInteraction?.(interaction)
    
    switch (interaction.type) {
      case 'LIKE':
      case 'LOVE':
      case 'FIRE':
      case 'MIND_BLOWN':
      case 'VIBE':
      case 'MOOD_MATCH':
        react(interaction.postId, interaction.type, interaction.mood)
        break
    }
  }, [onInteraction, react])



  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target !== document.body) return

      switch (event.key) {
        case ' ': // Spacebar
          event.preventDefault()
          // Toggle play/pause for current item
          break
        case 'r':
          event.preventDefault()
          refresh()
          break
        case 'm':
          event.preventDefault()
          clearMoods()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [refresh, clearMoods])

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen p-4">
        <p className="text-destructive mb-4">Failed to load feed: {error}</p>
        <Button onClick={refresh} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className={cn("relative h-screen bg-black overflow-hidden", className)}>
      {/* Feed Navigation - TODO: Implement */}
      <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/50 to-transparent">
        <div className="p-4 text-white">
          Feed: {feedType}
        </div>
      </div>

      {/* Mood Selector - TODO: Implement */}
      {selectedMoods.length > 0 && (
        <div className="absolute top-16 left-0 right-0 z-20 bg-gradient-to-b from-black/30 to-transparent">
          <div className="p-4 text-white">
            Moods: {selectedMoods.join(', ')}
          </div>
        </div>
      )}

      {/* Main Feed Container */}
      <div
        ref={(el) => {
          // Use callback ref to assign to both refs
          if (el) {
            (containerRef as any).current = el;
            (infiniteScrollHook.containerRef as any).current = el
          }
        }}
        className="h-full overflow-y-auto snap-y snap-mandatory scrollbar-hide"
        style={{ scrollBehavior: 'smooth' }}
        tabIndex={0}
        role="feed"
        aria-label={`${feedType} feed`}
      >
        {items.map((item, index) => (
          <div
            key={item.post.id}
            ref={(el) => {
              if (el) {
                observeElement(el)
              }
            }}
            data-post-id={item.post.id}
            className="snap-start"
          >
            <EnhancedContentCard
              item={item}
              isActive={index === currentIndex}
              autoPlay={autoPlay && index === currentIndex}
              onInteraction={handleInteraction}
              onRegisterMedia={registerMediaElement}
              onUnregisterMedia={unregisterMediaElement}
              className="w-full h-full"
            />
          </div>
        ))}

        {/* Loading indicator */}
        {loading && (
          <div className="h-screen flex items-center justify-center snap-start">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
        )}

        {/* End of feed indicator */}
        {!hasMore && items.length > 0 && (
          <div className="h-screen flex items-center justify-center snap-start">
            <div className="text-center text-white/70">
              <p className="text-lg mb-2">You've reached the end!</p>
              <Button onClick={refresh} variant="outline" size="sm">
                Refresh Feed
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Scroll indicators */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
        <div className="flex flex-col space-y-1">
          {items.slice(Math.max(0, currentIndex - 2), currentIndex + 3).map((_, relativeIndex) => {
            const actualIndex = Math.max(0, currentIndex - 2) + relativeIndex
            const isActive = actualIndex === currentIndex
            
            return (
              <button
                key={actualIndex}
                onClick={() => infiniteScrollHook.scrollToIndex(actualIndex)}
                className={cn(
                  "w-1 h-8 rounded-full transition-all duration-200",
                  isActive 
                    ? "bg-white" 
                    : "bg-white/30 hover:bg-white/50"
                )}
                aria-label={`Go to item ${actualIndex + 1}`}
              />
            )
          })}
        </div>
      </div>

      {/* Debug info (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-4 left-4 z-20 bg-black/50 text-white text-xs p-2 rounded">
          <div>Index: {currentIndex + 1}/{items.length}</div>
          <div>Scrolling: {isScrolling ? 'Yes' : 'No'}</div>
          <div>Scrolling: {isScrolling ? 'Yes' : 'No'}</div>
          <div>Has More: {hasMore ? 'Yes' : 'No'}</div>
          <div>Loading: {loading ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  )
}
