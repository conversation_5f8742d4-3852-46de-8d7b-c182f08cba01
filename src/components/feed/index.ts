// Enhanced Feed Components
export { VerticalFeedContainer } from './vertical-feed-container'
export { EnhancedContentCard } from './enhanced-content-card'
export { EnhancedFeedNavigation, CompactFeedNavigation } from './enhanced-feed-navigation'
export { EnhancedMoodSelector } from './enhanced-mood-selector'
export { EnhancedFeedControls } from './enhanced-feed-controls'

// Legacy components (to be implemented)
// export { ContentCard } from './content-card'
// export { FeedControls } from './feed-controls'
// export { MoodSelector } from './mood-selector'
// export { CreatorInfo } from './creator-info'
// export { FeedNavigation } from './feed-navigation'

// Hook exports
export { useFeedData } from '@/hooks/feed/use-feed-data'
export { useInfiniteScroll } from '@/hooks/feed/use-infinite-scroll'
export { useMoodFilter } from '@/hooks/feed/use-mood-filter'
export { useContentInteractions } from '@/hooks/feed/use-content-interactions'
export { useVideoPlayer } from '@/hooks/feed/use-video-player'

// Store exports
export { useFeedStore } from '@/lib/stores/feed-store'
export { useUserPreferencesStore } from '@/lib/stores/user-preferences-store'
export { useInteractionStore } from '@/lib/stores/interaction-store'
export { usePlayerStore } from '@/lib/stores/enhanced-player-store'

// Player Integration Components
export {
  FeedVideoPlayer,
  HVPPYContentPlayer,
  PlaylistPlayer,
  MoodPlayer
} from '@/components/player'

// Complete Feed Examples
export {
  EnhancedFeedExample,
  MobileEnhancedFeed,
  DesktopEnhancedFeed
} from './enhanced-feed-example'

// Type exports
export type {
  FeedItem,
  FeedType,
  FeedFilters,
  FeedPreferences,
  InteractionData,
  VideoPlayerState,
  VerticalFeedContainerProps,
  ContentCardProps,
  FeedControlsProps,
  MoodSelectorProps,
  CreatorInfoProps,
  FeedNavigationProps,
} from '@/types/feed'
