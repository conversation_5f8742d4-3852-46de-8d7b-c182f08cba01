// Video player implementation for HVPPY Central Media Player System

import { BaseMediaPlayer } from './base-player'
import { VideoPlayer, PlayerConfig, PlaybackQuality } from './types'

export class HVPPYVideoPlayer extends BaseMediaPlayer implements VideoPlayer {
  private videoElement: HTMLVideoElement
  private resizeObserver?: ResizeObserver
  private aspectRatio: number = 16 / 9

  constructor(element: HTMLVideoElement, config: Partial<PlayerConfig> = {}) {
    super(element, config)
    this.videoElement = element
    this.setupVideoEventListeners()
    this.setupResizeObserver()
  }

  private setupVideoEventListeners(): void {
    // Fullscreen events
    document.addEventListener('fullscreenchange', () => {
      const isFullscreen = document.fullscreenElement === this.videoElement
      this.updateState({ isFullscreen })
      this.emit('onFullscreenChange', isFullscreen)
    })

    // Picture-in-Picture events
    this.videoElement.addEventListener('enterpictureinpicture', () => {
      this.updateState({ isPiP: true })
      this.emit('onPiPChange', true)
    })

    this.videoElement.addEventListener('leavepictureinpicture', () => {
      this.updateState({ isPiP: false })
      this.emit('onPiPChange', false)
    })

    // Video dimension changes
    this.videoElement.addEventListener('loadedmetadata', () => {
      this.updateAspectRatio()
    })

    this.videoElement.addEventListener('resize', () => {
      this.updateAspectRatio()
    })
  }

  private setupResizeObserver(): void {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(() => {
        this.updateAspectRatio()
      })
      this.resizeObserver.observe(this.videoElement)
    }
  }

  private updateAspectRatio(): void {
    if (this.videoElement.videoWidth && this.videoElement.videoHeight) {
      this.aspectRatio = this.videoElement.videoWidth / this.videoElement.videoHeight
    }
  }

  // Video-specific methods
  async enterFullscreen(): Promise<void> {
    if (!document.fullscreenElement && this.config.fullscreen) {
      try {
        await this.videoElement.requestFullscreen()
      } catch (error) {
        console.error('Failed to enter fullscreen:', error)
        throw error
      }
    }
  }

  async exitFullscreen(): Promise<void> {
    if (document.fullscreenElement === this.videoElement) {
      try {
        await document.exitFullscreen()
      } catch (error) {
        console.error('Failed to exit fullscreen:', error)
        throw error
      }
    }
  }

  async enterPiP(): Promise<void> {
    if (!this.state.isPiP && this.config.pip && 'pictureInPictureEnabled' in document) {
      try {
        await this.videoElement.requestPictureInPicture()
      } catch (error) {
        console.error('Failed to enter Picture-in-Picture:', error)
        throw error
      }
    }
  }

  async exitPiP(): Promise<void> {
    if (this.state.isPiP && 'pictureInPictureElement' in document) {
      try {
        await document.exitPictureInPicture()
      } catch (error) {
        console.error('Failed to exit Picture-in-Picture:', error)
        throw error
      }
    }
  }

  getVideoElement(): HTMLVideoElement {
    return this.videoElement
  }

  setSize(width: number, height: number): void {
    this.videoElement.style.width = `${width}px`
    this.videoElement.style.height = `${height}px`
  }

  getAspectRatio(): number {
    return this.aspectRatio
  }

  // Text tracks (subtitles/captions)
  getTextTracks(): TextTrack[] {
    return Array.from(this.videoElement.textTracks)
  }

  setActiveTextTrack(track: TextTrack | null): void {
    // Disable all tracks first
    Array.from(this.videoElement.textTracks).forEach(t => {
      t.mode = 'disabled'
    })

    // Enable the selected track
    if (track) {
      track.mode = 'showing'
    }
  }

  // Quality management for video
  setQuality(quality: PlaybackQuality): void {
    // This is a simplified implementation
    // In a real-world scenario, you'd need to handle multiple source URLs
    // or integrate with adaptive streaming protocols like HLS or DASH
    
    if (this.currentSource) {
      const currentTime = this.getCurrentTime()
      const wasPlaying = this.state.state === 'playing'
      
      // Store quality preference
      this.state.quality = quality
      
      // In a full implementation, you would:
      // 1. Find the appropriate source URL for the quality
      // 2. Switch the video source
      // 3. Restore playback position and state
      
      console.log(`Quality changed to: ${quality}`)
      this.emit('onQualityChange', quality)
      
      // Restore playback state
      if (wasPlaying) {
        this.seek(currentTime)
        this.play().catch(console.error)
      }
    }
  }

  // Enhanced video controls
  toggleFullscreen(): Promise<void> {
    return this.state.isFullscreen ? this.exitFullscreen() : this.enterFullscreen()
  }

  togglePiP(): Promise<void> {
    return this.state.isPiP ? this.exitPiP() : this.enterPiP()
  }

  // Video-specific utilities
  captureFrame(): string | null {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) return null
      
      canvas.width = this.videoElement.videoWidth
      canvas.height = this.videoElement.videoHeight
      
      ctx.drawImage(this.videoElement, 0, 0)
      
      return canvas.toDataURL('image/png')
    } catch (error) {
      console.error('Failed to capture frame:', error)
      return null
    }
  }

  // Keyboard shortcuts
  handleKeyboard(event: KeyboardEvent): boolean {
    if (!this.config.keyboard) return false

    switch (event.code) {
      case 'Space':
        event.preventDefault()
        if (this.state.state === 'playing') {
          this.pause()
        } else {
          this.play().catch(console.error)
        }
        return true

      case 'ArrowLeft':
        event.preventDefault()
        this.seek(Math.max(0, this.getCurrentTime() - 10))
        return true

      case 'ArrowRight':
        event.preventDefault()
        this.seek(Math.min(this.getDuration(), this.getCurrentTime() + 10))
        return true

      case 'ArrowUp':
        event.preventDefault()
        this.setVolume(Math.min(1, this.getVolume() + 0.1))
        return true

      case 'ArrowDown':
        event.preventDefault()
        this.setVolume(Math.max(0, this.getVolume() - 0.1))
        return true

      case 'KeyM':
        event.preventDefault()
        this.setMuted(!this.isMuted())
        return true

      case 'KeyF':
        event.preventDefault()
        this.toggleFullscreen().catch(console.error)
        return true

      case 'KeyP':
        event.preventDefault()
        this.togglePiP().catch(console.error)
        return true

      default:
        return false
    }
  }

  // Cleanup
  destroy(): void {
    // Clean up video-specific resources
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }

    // Exit fullscreen and PiP if active
    if (this.state.isFullscreen) {
      this.exitFullscreen().catch(console.error)
    }
    
    if (this.state.isPiP) {
      this.exitPiP().catch(console.error)
    }

    // Call parent cleanup
    super.destroy()
  }
}

// Factory function for creating video players
export function createVideoPlayer(
  element: HTMLVideoElement, 
  config: Partial<PlayerConfig> = {}
): VideoPlayer {
  return new HVPPYVideoPlayer(element, config)
}

// Utility functions
export function isVideoSupported(): boolean {
  const video = document.createElement('video')
  return !!(video.canPlayType)
}

export function getSupportedVideoFormats(): string[] {
  const video = document.createElement('video')
  const formats = [
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/quicktime',
    'video/x-msvideo'
  ]
  
  return formats.filter(format => {
    const support = video.canPlayType(format)
    return support === 'probably' || support === 'maybe'
  })
}

export function getOptimalVideoQuality(
  availableQualities: PlaybackQuality[],
  bandwidth?: number,
  screenSize?: { width: number; height: number }
): PlaybackQuality {
  // Simple quality selection logic
  // In a real implementation, this would be more sophisticated
  
  if (!screenSize) {
    return PlaybackQuality.AUTO
  }
  
  const { width, height } = screenSize
  const pixels = width * height
  
  if (pixels >= 3840 * 2160 && availableQualities.includes(PlaybackQuality.UHD)) {
    return PlaybackQuality.UHD
  } else if (pixels >= 1920 * 1080 && availableQualities.includes(PlaybackQuality.HD)) {
    return PlaybackQuality.HD
  } else if (pixels >= 1280 * 720 && availableQualities.includes(PlaybackQuality.HIGH)) {
    return PlaybackQuality.HIGH
  } else if (pixels >= 854 * 480 && availableQualities.includes(PlaybackQuality.MEDIUM)) {
    return PlaybackQuality.MEDIUM
  } else {
    return PlaybackQuality.LOW
  }
}
