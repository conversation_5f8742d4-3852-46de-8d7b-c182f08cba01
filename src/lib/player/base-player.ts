// Base player implementation for HVPPY Central Media Player System

import {
  BasePlayer,
  MediaSource,
  PlaybackState as PlayerStateEnum,
  PlayerState as PlayerStateInterface,
  PlayerEvents,
  PlayerError,
  PlayerConfig
} from './types'

export abstract class BaseMediaPlayer implements BasePlayer {
  protected element: HTMLMediaElement
  protected config: PlayerConfig
  protected currentSource?: MediaSource
  protected eventListeners: Map<keyof PlayerEvents, Set<Function>> = new Map()
  protected lastTimeUpdate: number = 0
  protected timeUpdateThrottle: number = 100 // Throttle time updates to 100ms
  protected state: PlayerStateInterface = {
    state: PlayerStateEnum.IDLE,
    currentTime: 0,
    duration: 0,
    bufferedTime: 0,
    volume: 0.8,
    muted: false,
    playbackRate: 1,
    quality: 'auto',
    isFullscreen: false,
    isPiP: false,
    isLive: false
  }

  constructor(element: HTMLMediaElement, config: Partial<PlayerConfig> = {}) {
    this.element = element
    this.config = {
      autoPlay: false,
      loop: false,
      muted: false,
      volume: 0.8,
      playbackRate: 1,
      quality: 'auto',
      preload: 'metadata',
      controls: true,
      keyboard: true,
      fullscreen: true,
      pip: true,
      airplay: true,
      chromecast: false,
      ...config
    }

    this.initializeElement()
    this.setupEventListeners()
  }

  protected initializeElement(): void {
    this.element.preload = this.config.preload
    this.element.controls = this.config.controls
    this.element.loop = this.config.loop
    this.element.muted = this.config.muted
    this.element.volume = this.config.volume
    this.element.playbackRate = this.config.playbackRate

    // Set initial state
    this.state.volume = this.config.volume
    this.state.muted = this.config.muted
    this.state.playbackRate = this.config.playbackRate
  }

  protected setupEventListeners(): void {
    // Playback events
    this.element.addEventListener('loadstart', () => {
      this.updateState({ state: PlayerStateEnum.LOADING })
    })

    this.element.addEventListener('loadedmetadata', () => {
      this.updateState({ 
        state: PlayerStateEnum.READY,
        duration: this.element.duration || 0
      })
      this.emit('onDurationChange', this.element.duration || 0)
    })

    this.element.addEventListener('canplay', () => {
      if (this.state.state === PlayerStateEnum.LOADING) {
        this.updateState({ state: PlayerStateEnum.READY })
      }
    })

    this.element.addEventListener('play', () => {
      this.updateState({ state: PlayerStateEnum.PLAYING })
    })

    this.element.addEventListener('pause', () => {
      this.updateState({ state: PlayerStateEnum.PAUSED })
    })

    this.element.addEventListener('ended', () => {
      this.updateState({ state: PlayerStateEnum.ENDED })
      this.emit('onEnded')
    })

    this.element.addEventListener('waiting', () => {
      this.updateState({ state: PlayerStateEnum.BUFFERING })
    })

    this.element.addEventListener('playing', () => {
      if (this.state.state === PlayerStateEnum.BUFFERING) {
        this.updateState({ state: PlayerStateEnum.PLAYING })
      }
    })

    // Time updates (throttled to prevent excessive re-renders)
    this.element.addEventListener('timeupdate', () => {
      const now = Date.now()
      const currentTime = this.element.currentTime

      // Only update if enough time has passed and time has actually changed
      if (now - this.lastTimeUpdate >= this.timeUpdateThrottle &&
          Math.abs(currentTime - this.state.currentTime) > 0.1) {
        this.updateState({ currentTime })
        this.emit('onTimeUpdate', currentTime)
        this.lastTimeUpdate = now
      }
    })

    this.element.addEventListener('durationchange', () => {
      this.updateState({ duration: this.element.duration || 0 })
      this.emit('onDurationChange', this.element.duration || 0)
    })

    // Progress
    this.element.addEventListener('progress', () => {
      const buffered = this.element.buffered
      if (buffered.length > 0) {
        const bufferedTime = buffered.end(buffered.length - 1)
        this.updateState({ bufferedTime })
        this.emit('onProgress', bufferedTime)
      }
    })

    // Volume
    this.element.addEventListener('volumechange', () => {
      this.updateState({ 
        volume: this.element.volume,
        muted: this.element.muted
      })
      this.emit('onVolumeChange', this.element.volume, this.element.muted)
    })

    // Rate change
    this.element.addEventListener('ratechange', () => {
      this.updateState({ playbackRate: this.element.playbackRate })
    })

    // Error handling
    this.element.addEventListener('error', () => {
      const error: PlayerError = {
        code: this.element.error?.code || 0,
        message: this.element.error?.message || 'Unknown error',
        details: this.element.error
      }
      this.updateState({ 
        state: PlayerStateEnum.ERROR,
        error
      })
      this.emit('onError', error)
    })
  }

  protected updateState(updates: Partial<PlayerStateInterface>): void {
    const previousState = this.state
    const newState = { ...this.state, ...updates }

    // Only update and emit if state actually changed
    const hasChanged = Object.keys(updates).some(key => {
      const typedKey = key as keyof PlayerStateInterface
      return previousState[typedKey] !== newState[typedKey]
    })

    if (hasChanged) {
      this.state = newState
      this.emit('onStateChange', this.state)
    }
  }

  protected emit<K extends keyof PlayerEvents>(event: K, ...args: Parameters<PlayerEvents[K]>): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          (listener as any)(...args)
        } catch (error) {
          console.error(`Error in ${event} listener:`, error)
        }
      })
    }
  }

  // Public API methods
  async load(source: MediaSource): Promise<void> {
    this.currentSource = source
    this.updateState({ state: PlayerStateEnum.LOADING })
    
    try {
      this.element.src = source.url
      await new Promise((resolve, reject) => {
        const onLoadedMetadata = () => {
          this.element.removeEventListener('loadedmetadata', onLoadedMetadata)
          this.element.removeEventListener('error', onError)
          resolve(void 0)
        }
        
        const onError = (event: Event) => {
          this.element.removeEventListener('loadedmetadata', onLoadedMetadata)
          this.element.removeEventListener('error', onError)
          const mediaError = this.element.error
          const errorMessage = mediaError
            ? `Media error: ${mediaError.message || 'Unknown media error'} (Code: ${mediaError.code})`
            : 'Failed to load media'
          reject(new Error(errorMessage))
        }
        
        this.element.addEventListener('loadedmetadata', onLoadedMetadata)
        this.element.addEventListener('error', onError)
      })
    } catch (error) {
      this.updateState({ 
        state: PlayerStateEnum.ERROR,
        error: {
          code: -1,
          message: 'Failed to load media source',
          details: error
        }
      })
      throw error
    }
  }

  async play(): Promise<void> {
    try {
      await this.element.play()
    } catch (error) {
      this.updateState({
        state: PlayerStateEnum.ERROR,
        error: {
          code: -1,
          message: 'Failed to play media',
          details: error
        }
      })
      throw error
    }
  }

  pause(): void {
    this.element.pause()
  }

  stop(): void {
    this.element.pause()
    this.element.currentTime = 0
    this.updateState({ 
      state: PlayerStateEnum.READY,
      currentTime: 0
    })
  }

  seek(time: number): void {
    if (time >= 0 && time <= this.element.duration) {
      this.element.currentTime = time
    }
  }

  setVolume(volume: number): void {
    const clampedVolume = Math.max(0, Math.min(1, volume))
    this.element.volume = clampedVolume
  }

  setMuted(muted: boolean): void {
    this.element.muted = muted
  }

  setPlaybackRate(rate: number): void {
    const clampedRate = Math.max(0.25, Math.min(4, rate))
    this.element.playbackRate = clampedRate
  }

  setQuality(quality: any): void {
    // Implementation depends on the specific player type
    // This is a placeholder for the base implementation
    console.warn('setQuality not implemented in base player')
  }

  // State getters
  getState(): PlayerStateInterface {
    return { ...this.state }
  }

  getCurrentTime(): number {
    return this.element.currentTime
  }

  getDuration(): number {
    return this.element.duration || 0
  }

  getVolume(): number {
    return this.element.volume
  }

  isMuted(): boolean {
    return this.element.muted
  }

  // Event handling
  on<K extends keyof PlayerEvents>(event: K, handler: PlayerEvents[K]): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set())
    }
    this.eventListeners.get(event)!.add(handler)
  }

  off<K extends keyof PlayerEvents>(event: K, handler: PlayerEvents[K]): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.delete(handler)
    }
  }

  destroy(): void {
    // Remove all event listeners
    this.eventListeners.clear()
    
    // Clean up element
    this.element.pause()
    this.element.src = ''
    this.element.load()
    
    // Reset state
    this.state = {
      state: PlayerStateEnum.IDLE,
      currentTime: 0,
      duration: 0,
      bufferedTime: 0,
      volume: 0.8,
      muted: false,
      playbackRate: 1,
      quality: 'auto',
      isFullscreen: false,
      isPiP: false,
      isLive: false
    }
  }
}
