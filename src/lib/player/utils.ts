// Utility functions for HVPPY Central Media Player System

import { MediaType, MediaSource, PlaybackQuality, AudioQuality } from './types'

// Time formatting utilities
export function formatTime(seconds: number): string {
  if (!isFinite(seconds) || seconds < 0) return '0:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

export function parseTime(timeString: string): number {
  const parts = timeString.split(':').map(Number)
  
  if (parts.length === 2) {
    // MM:SS format
    return parts[0] * 60 + parts[1]
  } else if (parts.length === 3) {
    // HH:MM:SS format
    return parts[0] * 3600 + parts[1] * 60 + parts[2]
  }
  
  return 0
}

// File and URL utilities
export function getFileExtension(url: string): string {
  // Handle both absolute and relative URLs
  let pathname: string
  try {
    // Try to parse as absolute URL first
    pathname = new URL(url).pathname
  } catch {
    // If it fails, treat as relative path
    pathname = url.startsWith('/') ? url : `/${url}`
  }

  const lastDot = pathname.lastIndexOf('.')
  return lastDot > 0 ? pathname.slice(lastDot + 1).toLowerCase() : ''
}

export function detectMediaType(url: string): MediaType {
  const extension = getFileExtension(url)
  
  const videoExtensions = ['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv', 'mkv']
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma']
  
  if (videoExtensions.includes(extension)) {
    return MediaType.VIDEO
  } else if (audioExtensions.includes(extension)) {
    return MediaType.AUDIO
  }
  
  return MediaType.VIDEO // Default fallback
}

export function createMediaSource(
  id: string,
  url: string,
  options: Partial<MediaSource> = {}
): MediaSource {
  const type = options.type || detectMediaType(url)
  const extension = getFileExtension(url)
  
  return {
    id,
    url,
    type,
    format: extension,
    quality: options.quality || (type === MediaType.VIDEO ? PlaybackQuality.AUTO : AudioQuality.MEDIUM),
    ...options
  }
}

// Quality utilities
export function getQualityLabel(quality: PlaybackQuality | AudioQuality): string {
  const labels: Record<string, string> = {
    [PlaybackQuality.AUTO]: 'Auto',
    [PlaybackQuality.LOW]: '360p',
    [PlaybackQuality.MEDIUM]: '480p',
    [PlaybackQuality.HIGH]: '720p',
    [PlaybackQuality.HD]: '1080p',
    [PlaybackQuality.UHD]: '4K',
    [AudioQuality.LOW]: '128 kbps',
    [AudioQuality.MEDIUM]: '256 kbps',
    [AudioQuality.HIGH]: '320 kbps',
    [AudioQuality.LOSSLESS]: 'Lossless'
  }
  
  return labels[quality] || quality
}

export function compareQuality(a: PlaybackQuality | AudioQuality, b: PlaybackQuality | AudioQuality): number {
  const videoOrder = [
    PlaybackQuality.LOW,
    PlaybackQuality.MEDIUM,
    PlaybackQuality.HIGH,
    PlaybackQuality.HD,
    PlaybackQuality.UHD,
    PlaybackQuality.AUTO
  ]
  
  const audioOrder = [
    AudioQuality.LOW,
    AudioQuality.MEDIUM,
    AudioQuality.HIGH,
    AudioQuality.LOSSLESS
  ]
  
  const aIndex = videoOrder.indexOf(a as PlaybackQuality) !== -1 
    ? videoOrder.indexOf(a as PlaybackQuality)
    : audioOrder.indexOf(a as AudioQuality)
    
  const bIndex = videoOrder.indexOf(b as PlaybackQuality) !== -1
    ? videoOrder.indexOf(b as PlaybackQuality)
    : audioOrder.indexOf(b as AudioQuality)
  
  return aIndex - bIndex
}

// Bandwidth and network utilities
export function estimateBandwidth(): Promise<number> {
  return new Promise((resolve) => {
    // Simple bandwidth estimation using a small test download
    const testUrl = '/api/bandwidth-test' // Would need to implement this endpoint
    const startTime = performance.now()
    
    fetch(testUrl, { cache: 'no-cache' })
      .then(response => response.blob())
      .then(blob => {
        const endTime = performance.now()
        const duration = (endTime - startTime) / 1000 // Convert to seconds
        const bits = blob.size * 8
        const bandwidth = bits / duration // bits per second
        resolve(bandwidth)
      })
      .catch(() => {
        // Fallback to a conservative estimate
        resolve(1000000) // 1 Mbps
      })
  })
}

export function getOptimalQualityForBandwidth(
  bandwidth: number,
  mediaType: MediaType
): PlaybackQuality | AudioQuality {
  if (mediaType === MediaType.VIDEO) {
    // Video quality recommendations based on bandwidth (in bps)
    if (bandwidth >= 25000000) return PlaybackQuality.UHD    // 25+ Mbps for 4K
    if (bandwidth >= 8000000) return PlaybackQuality.HD      // 8+ Mbps for 1080p
    if (bandwidth >= 5000000) return PlaybackQuality.HIGH    // 5+ Mbps for 720p
    if (bandwidth >= 2500000) return PlaybackQuality.MEDIUM  // 2.5+ Mbps for 480p
    return PlaybackQuality.LOW                               // < 2.5 Mbps for 360p
  } else {
    // Audio quality recommendations
    if (bandwidth >= 1411200) return AudioQuality.LOSSLESS  // 1.4+ Mbps for lossless
    if (bandwidth >= 320000) return AudioQuality.HIGH       // 320+ kbps
    if (bandwidth >= 256000) return AudioQuality.MEDIUM     // 256+ kbps
    return AudioQuality.LOW                                  // < 256 kbps
  }
}

// Storage utilities
export function savePlayerPreferences(preferences: any): void {
  try {
    localStorage.setItem('hvppy-player-preferences', JSON.stringify(preferences))
  } catch (error) {
    console.warn('Failed to save player preferences:', error)
  }
}

export function loadPlayerPreferences(): any {
  try {
    const stored = localStorage.getItem('hvppy-player-preferences')
    return stored ? JSON.parse(stored) : {}
  } catch (error) {
    console.warn('Failed to load player preferences:', error)
    return {}
  }
}

// Accessibility utilities
export function announceToScreenReader(message: string): void {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.style.position = 'absolute'
  announcement.style.left = '-10000px'
  announcement.style.width = '1px'
  announcement.style.height = '1px'
  announcement.style.overflow = 'hidden'
  
  document.body.appendChild(announcement)
  announcement.textContent = message
  
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

// Performance utilities
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  let lastExecTime = 0
  
  return (...args: Parameters<T>) => {
    const currentTime = Date.now()
    
    if (currentTime - lastExecTime > delay) {
      func(...args)
      lastExecTime = currentTime
    } else {
      if (timeoutId) clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        func(...args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

// Error handling utilities
export function createPlayerError(code: number, message: string, details?: any) {
  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString()
  }
}

export function getErrorMessage(error: any): string {
  if (error?.message) return error.message
  if (typeof error === 'string') return error
  return 'An unknown error occurred'
}

// Validation utilities
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export function isValidTimeRange(start: number, end: number, duration: number): boolean {
  return start >= 0 && end <= duration && start < end
}

export function clamp(value: number, min: number, max: number): number {
  return Math.max(min, Math.min(max, value))
}

// Device detection utilities
export function isMobile(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

export function isTouch(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

export function supportsFullscreen(): boolean {
  const element = document.createElement('div')
  return !!(
    element.requestFullscreen ||
    (element as any).webkitRequestFullscreen ||
    (element as any).mozRequestFullScreen ||
    (element as any).msRequestFullscreen
  )
}

export function supportsPictureInPicture(): boolean {
  return 'pictureInPictureEnabled' in document
}

// Content classification utilities (integration with HVPPY content system)
export function getMoodFromContent(contentId: string): string[] {
  // This would integrate with the content classification system
  // For now, return empty array as placeholder
  return []
}

export function getContentMetadata(contentId: string): any {
  // This would fetch metadata from the content classification system
  // For now, return null as placeholder
  return null
}
