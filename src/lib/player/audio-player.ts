// Audio player implementation for HVPPY Central Media Player System

import { BaseMediaPlayer } from './base-player'
import { AudioPlayer, PlayerConfig, AudioQuality } from './types'

export class HVPPYAudioPlayer extends BaseMediaPlayer implements AudioPlayer {
  private audioElement: HTMLAudioElement
  private audioContext?: AudioContext
  private analyser?: AnalyserNode
  private gainNode?: GainNode
  private sourceNode?: MediaElementAudioSourceNode
  private frequencyData?: Uint8Array
  private waveformData?: Uint8Array
  private equalizerBands: number[] = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] // 10-band EQ
  private equalizerFilters: BiquadFilterNode[] = []

  constructor(element: HTMLAudioElement, config: Partial<PlayerConfig> = {}) {
    super(element, config)
    this.audioElement = element
    this.initializeAudioContext()
  }

  private async initializeAudioContext(): Promise<void> {
    try {
      // Create audio context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      // Create analyser for visualizations
      this.analyser = this.audioContext.createAnalyser()
      this.analyser.fftSize = 2048
      this.analyser.smoothingTimeConstant = 0.8
      
      // Create gain node for volume control
      this.gainNode = this.audioContext.createGain()
      
      // Create source node
      this.sourceNode = this.audioContext.createMediaElementSource(this.audioElement)
      
      // Initialize frequency and waveform data arrays
      this.frequencyData = new Uint8Array(this.analyser.frequencyBinCount)
      this.waveformData = new Uint8Array(this.analyser.frequencyBinCount)
      
      // Set up equalizer
      this.initializeEqualizer()
      
      // Connect nodes: source -> equalizer -> gain -> analyser -> destination
      this.connectAudioNodes()
      
    } catch (error) {
      console.error('Failed to initialize audio context:', error)
    }
  }

  private initializeEqualizer(): void {
    if (!this.audioContext) return

    // Create 10-band equalizer with standard frequencies
    const frequencies = [60, 170, 310, 600, 1000, 3000, 6000, 12000, 14000, 16000]
    
    this.equalizerFilters = frequencies.map((freq, index) => {
      const filter = this.audioContext!.createBiquadFilter()
      filter.type = index === 0 ? 'lowshelf' : index === frequencies.length - 1 ? 'highshelf' : 'peaking'
      filter.frequency.value = freq
      filter.Q.value = 1
      filter.gain.value = 0
      return filter
    })
  }

  private connectAudioNodes(): void {
    if (!this.sourceNode || !this.gainNode || !this.analyser || !this.audioContext) return

    let currentNode: AudioNode = this.sourceNode

    // Connect through equalizer filters
    this.equalizerFilters.forEach(filter => {
      currentNode.connect(filter)
      currentNode = filter
    })

    // Connect to gain and analyser
    currentNode.connect(this.gainNode)
    this.gainNode.connect(this.analyser)
    this.analyser.connect(this.audioContext.destination)
  }

  // Audio-specific methods
  getAudioElement(): HTMLAudioElement {
    return this.audioElement
  }

  getFrequencyData(): Uint8Array {
    if (this.analyser && this.frequencyData) {
      this.analyser.getByteFrequencyData(this.frequencyData)
      return this.frequencyData
    }
    return new Uint8Array(0)
  }

  getWaveformData(): Uint8Array {
    if (this.analyser && this.waveformData) {
      this.analyser.getByteTimeDomainData(this.waveformData)
      return this.waveformData
    }
    return new Uint8Array(0)
  }

  setEqualizer(bands: number[]): void {
    if (bands.length !== this.equalizerBands.length) {
      console.warn('Equalizer bands array must have exactly 10 elements')
      return
    }

    this.equalizerBands = [...bands]
    
    // Apply gains to filters
    this.equalizerFilters.forEach((filter, index) => {
      if (filter && bands[index] !== undefined) {
        filter.gain.value = Math.max(-20, Math.min(20, bands[index]))
      }
    })
  }

  getEqualizer(): number[] {
    return [...this.equalizerBands]
  }

  // Enhanced volume control using Web Audio API
  setVolume(volume: number): void {
    super.setVolume(volume)
    
    if (this.gainNode) {
      // Use exponential scaling for more natural volume perception
      const gain = volume * volume
      this.gainNode.gain.value = gain
    }
  }

  // Audio quality management
  setQuality(quality: AudioQuality): void {
    if (this.currentSource) {
      const currentTime = this.getCurrentTime()
      const wasPlaying = this.state.state === 'playing'
      
      // Store quality preference
      this.state.quality = quality
      
      console.log(`Audio quality changed to: ${quality}`)
      this.emit('onQualityChange', quality)
      
      // In a real implementation, you would switch to a different source URL
      // based on the quality setting
      
      // Restore playback state
      if (wasPlaying) {
        this.seek(currentTime)
        this.play().catch(console.error)
      }
    }
  }

  // Audio analysis utilities
  getAverageFrequency(): number {
    const frequencyData = this.getFrequencyData()
    if (frequencyData.length === 0) return 0
    
    const sum = frequencyData.reduce((acc, val) => acc + val, 0)
    return sum / frequencyData.length
  }

  getDominantFrequency(): number {
    const frequencyData = this.getFrequencyData()
    if (frequencyData.length === 0) return 0
    
    let maxIndex = 0
    let maxValue = 0
    
    for (let i = 0; i < frequencyData.length; i++) {
      if (frequencyData[i] > maxValue) {
        maxValue = frequencyData[i]
        maxIndex = i
      }
    }
    
    // Convert bin index to frequency
    const nyquist = (this.audioContext?.sampleRate || 44100) / 2
    return (maxIndex / frequencyData.length) * nyquist
  }

  getBassLevel(): number {
    const frequencyData = this.getFrequencyData()
    if (frequencyData.length === 0) return 0
    
    // Bass frequencies are typically 20-250 Hz
    // Calculate which bins correspond to this range
    const nyquist = (this.audioContext?.sampleRate || 44100) / 2
    const bassEndBin = Math.floor((250 / nyquist) * frequencyData.length)
    
    let sum = 0
    for (let i = 0; i < Math.min(bassEndBin, frequencyData.length); i++) {
      sum += frequencyData[i]
    }
    
    return sum / bassEndBin
  }

  getTrebleLevel(): number {
    const frequencyData = this.getFrequencyData()
    if (frequencyData.length === 0) return 0
    
    // Treble frequencies are typically 4000+ Hz
    const nyquist = (this.audioContext?.sampleRate || 44100) / 2
    const trebleStartBin = Math.floor((4000 / nyquist) * frequencyData.length)
    
    let sum = 0
    let count = 0
    for (let i = trebleStartBin; i < frequencyData.length; i++) {
      sum += frequencyData[i]
      count++
    }
    
    return count > 0 ? sum / count : 0
  }

  // Audio effects
  setReverbLevel(level: number): void {
    // Placeholder for reverb implementation
    // Would require a convolver node with impulse response
    console.log(`Reverb level set to: ${level}`)
  }

  setBassBoost(boost: number): void {
    // Boost the first few equalizer bands (bass frequencies)
    const newBands = [...this.equalizerBands]
    newBands[0] = Math.max(-20, Math.min(20, boost))
    newBands[1] = Math.max(-20, Math.min(20, boost * 0.7))
    newBands[2] = Math.max(-20, Math.min(20, boost * 0.4))
    this.setEqualizer(newBands)
  }

  setTrebleBoost(boost: number): void {
    // Boost the last few equalizer bands (treble frequencies)
    const newBands = [...this.equalizerBands]
    const lastIndex = newBands.length - 1
    newBands[lastIndex] = Math.max(-20, Math.min(20, boost))
    newBands[lastIndex - 1] = Math.max(-20, Math.min(20, boost * 0.7))
    newBands[lastIndex - 2] = Math.max(-20, Math.min(20, boost * 0.4))
    this.setEqualizer(newBands)
  }

  // Preset equalizer settings
  setEqualizerPreset(preset: string): void {
    const presets: Record<string, number[]> = {
      flat: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      rock: [5, 3, -1, -2, 1, 2, 4, 5, 5, 5],
      pop: [2, 1, 0, -1, -1, 0, 1, 2, 3, 3],
      jazz: [4, 3, 1, 2, -1, -1, 0, 1, 2, 3],
      classical: [5, 4, 3, 2, -1, -2, -1, 2, 3, 4],
      electronic: [6, 5, 1, 0, -2, 2, 1, 2, 5, 6],
      hip_hop: [6, 4, 1, 3, -1, -1, 1, 2, 3, 4],
      vocal: [2, 1, -1, -2, -1, 2, 4, 4, 3, 1]
    }

    const presetBands = presets[preset]
    if (presetBands) {
      this.setEqualizer(presetBands)
    } else {
      console.warn(`Unknown equalizer preset: ${preset}`)
    }
  }

  // Resume audio context if suspended (required for some browsers)
  async resumeAudioContext(): Promise<void> {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume()
      } catch (error) {
        console.error('Failed to resume audio context:', error)
      }
    }
  }

  // Cleanup
  destroy(): void {
    // Clean up Web Audio API resources
    if (this.audioContext) {
      this.audioContext.close().catch(console.error)
    }

    // Clear data arrays
    this.frequencyData = undefined
    this.waveformData = undefined

    // Call parent cleanup
    super.destroy()
  }
}

// Factory function for creating audio players
export function createAudioPlayer(
  element: HTMLAudioElement, 
  config: Partial<PlayerConfig> = {}
): AudioPlayer {
  return new HVPPYAudioPlayer(element, config)
}

// Utility functions
export function isAudioSupported(): boolean {
  const audio = document.createElement('audio')
  return !!(audio.canPlayType)
}

export function getSupportedAudioFormats(): string[] {
  const audio = document.createElement('audio')
  const formats = [
    'audio/mpeg',
    'audio/mp4',
    'audio/ogg',
    'audio/wav',
    'audio/webm',
    'audio/flac'
  ]
  
  return formats.filter(format => {
    const support = audio.canPlayType(format)
    return support === 'probably' || support === 'maybe'
  })
}

export function isWebAudioSupported(): boolean {
  return !!(window.AudioContext || (window as any).webkitAudioContext)
}
