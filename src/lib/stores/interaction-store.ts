import { create } from 'zustand'
import { UserInteractionState, InteractionData } from '@/types/feed'
import { ReactionType } from '@/types'

interface InteractionStore extends UserInteractionState {
  // Loading states
  loadingReactions: Set<string>
  loadingMemories: Set<string>
  loadingShares: Set<string>

  // Actions
  setReaction: (postId: string, type: ReactionType) => void
  removeReaction: (postId: string) => void
  addMemory: (postId: string) => void
  removeMemory: (postId: string) => void
  addShare: (postId: string) => void
  incrementView: (postId: string) => void
  updateWatchTime: (postId: string, time: number) => void
  
  // Loading states
  setReactionLoading: (postId: string, loading: boolean) => void
  setMemoryLoading: (postId: string, loading: boolean) => void
  setShareLoading: (postId: string, loading: boolean) => void
  
  // Bulk operations
  loadUserInteractions: (interactions: {
    reactions: Array<{ postId: string; type: ReactionType }>
    memories: string[]
    shares: string[]
    views: Array<{ postId: string; count: number }>
    watchTime: Array<{ postId: string; time: number }>
  }) => void
  
  // Analytics
  getEngagementData: () => {
    totalReactions: number
    totalMemories: number
    totalShares: number
    totalViews: number
    averageWatchTime: number
  }
  
  reset: () => void
}

const initialState: UserInteractionState = {
  reactions: new Map(),
  memories: new Set(),
  shares: new Set(),
  views: new Map(),
  watchTime: new Map(),
}

export const useInteractionStore = create<InteractionStore>((set, get) => ({
  ...initialState,
  loadingReactions: new Set(),
  loadingMemories: new Set(),
  loadingShares: new Set(),

  setReaction: (postId, type) => {
    const { reactions } = get()
    const newReactions = new Map(reactions)
    newReactions.set(postId, type)
    set({ reactions: newReactions })
  },

  removeReaction: (postId) => {
    const { reactions } = get()
    const newReactions = new Map(reactions)
    newReactions.delete(postId)
    set({ reactions: newReactions })
  },

  addMemory: (postId) => {
    const { memories } = get()
    const newMemories = new Set(memories)
    newMemories.add(postId)
    set({ memories: newMemories })
  },

  removeMemory: (postId) => {
    const { memories } = get()
    const newMemories = new Set(memories)
    newMemories.delete(postId)
    set({ memories: newMemories })
  },

  addShare: (postId) => {
    const { shares } = get()
    const newShares = new Set(shares)
    newShares.add(postId)
    set({ shares: newShares })
  },

  incrementView: (postId) => {
    const { views } = get()
    const newViews = new Map(views)
    const currentCount = newViews.get(postId) || 0
    newViews.set(postId, currentCount + 1)
    set({ views: newViews })
  },

  updateWatchTime: (postId, time) => {
    const { watchTime } = get()
    const newWatchTime = new Map(watchTime)
    const currentTime = newWatchTime.get(postId) || 0
    newWatchTime.set(postId, Math.max(currentTime, time))
    set({ watchTime: newWatchTime })
  },

  setReactionLoading: (postId, loading) => {
    const { loadingReactions } = get()
    const newLoading = new Set(loadingReactions)
    if (loading) {
      newLoading.add(postId)
    } else {
      newLoading.delete(postId)
    }
    set({ loadingReactions: newLoading })
  },

  setMemoryLoading: (postId, loading) => {
    const { loadingMemories } = get()
    const newLoading = new Set(loadingMemories)
    if (loading) {
      newLoading.add(postId)
    } else {
      newLoading.delete(postId)
    }
    set({ loadingMemories: newLoading })
  },

  setShareLoading: (postId, loading) => {
    const { loadingShares } = get()
    const newLoading = new Set(loadingShares)
    if (loading) {
      newLoading.add(postId)
    } else {
      newLoading.delete(postId)
    }
    set({ loadingShares: newLoading })
  },

  loadUserInteractions: (interactions) => {
    const reactionsMap = new Map()
    interactions.reactions.forEach(({ postId, type }) => {
      reactionsMap.set(postId, type)
    })

    const viewsMap = new Map()
    interactions.views.forEach(({ postId, count }) => {
      viewsMap.set(postId, count)
    })

    const watchTimeMap = new Map()
    interactions.watchTime.forEach(({ postId, time }) => {
      watchTimeMap.set(postId, time)
    })

    set({
      reactions: reactionsMap,
      memories: new Set(interactions.memories),
      shares: new Set(interactions.shares),
      views: viewsMap,
      watchTime: watchTimeMap,
    })
  },

  getEngagementData: () => {
    const { reactions, memories, shares, views, watchTime } = get()
    
    const totalViews = Array.from(views.values()).reduce((sum, count) => sum + count, 0)
    const totalWatchTime = Array.from(watchTime.values()).reduce((sum, time) => sum + time, 0)
    const averageWatchTime = watchTime.size > 0 ? totalWatchTime / watchTime.size : 0

    return {
      totalReactions: reactions.size,
      totalMemories: memories.size,
      totalShares: shares.size,
      totalViews,
      averageWatchTime,
    }
  },

  reset: () => {
    set({
      ...initialState,
      loadingReactions: new Set(),
      loadingMemories: new Set(),
      loadingShares: new Set(),
    })
  },
}))

// Selectors
export const selectUserReaction = (postId: string) => (state: InteractionStore) =>
  state.reactions.get(postId)

export const selectHasMemory = (postId: string) => (state: InteractionStore) =>
  state.memories.has(postId)

export const selectHasShared = (postId: string) => (state: InteractionStore) =>
  state.shares.has(postId)

export const selectViewCount = (postId: string) => (state: InteractionStore) =>
  state.views.get(postId) || 0

export const selectWatchTime = (postId: string) => (state: InteractionStore) =>
  state.watchTime.get(postId) || 0

export const selectIsReactionLoading = (postId: string) => (state: InteractionStore) =>
  state.loadingReactions.has(postId)

export const selectIsMemoryLoading = (postId: string) => (state: InteractionStore) =>
  state.loadingMemories.has(postId)

export const selectIsShareLoading = (postId: string) => (state: InteractionStore) =>
  state.loadingShares.has(postId)
