import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { FeedState, FeedType, FeedItem, FeedFilters } from '@/types/feed'

interface FeedStore extends FeedState {
  // Actions
  setCurrentFeed: (feedType: FeedType) => void
  setItems: (items: FeedItem[]) => void
  addItems: (items: FeedItem[]) => void
  updateItem: (postId: string, updates: Partial<FeedItem>) => void
  removeItem: (postId: string) => void
  setLoading: (loading: boolean) => void
  setError: (error?: string) => void
  setHasMore: (hasMore: boolean) => void
  setNextCursor: (cursor?: string) => void
  updateFilters: (filters: Partial<FeedFilters>) => void
  setCurrentIndex: (index: number) => void
  addPreloadedItem: (postId: string) => void
  clearFeed: () => void
  reset: () => void
}

const initialState: FeedState = {
  currentFeed: FeedType.DISCOVER,
  items: [],
  loading: false,
  error: undefined,
  hasMore: true,
  nextCursor: undefined,
  filters: {},
  currentIndex: 0,
  preloadedItems: new Set(),
}

export const useFeedStore = create<FeedStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    setCurrentFeed: (feedType) => {
      set({ currentFeed: feedType })
    },

    setItems: (items) => {
      set({ items, currentIndex: 0 })
    },

    addItems: (newItems) => {
      const { items } = get()
      const existingIds = new Set(items.map(item => item.id))
      const uniqueNewItems = newItems.filter(item => !existingIds.has(item.id))
      
      set({ 
        items: [...items, ...uniqueNewItems],
        loading: false 
      })
    },

    updateItem: (postId, updates) => {
      const { items } = get()
      const updatedItems = items.map(item => 
        item.post.id === postId 
          ? { ...item, ...updates }
          : item
      )
      set({ items: updatedItems })
    },

    removeItem: (postId) => {
      const { items, currentIndex } = get()
      const updatedItems = items.filter(item => item.post.id !== postId)
      const newIndex = currentIndex >= updatedItems.length 
        ? Math.max(0, updatedItems.length - 1)
        : currentIndex
      
      set({ 
        items: updatedItems,
        currentIndex: newIndex
      })
    },

    setLoading: (loading) => {
      set({ loading })
    },

    setError: (error) => {
      set({ error, loading: false })
    },

    setHasMore: (hasMore) => {
      set({ hasMore })
    },

    setNextCursor: (nextCursor) => {
      set({ nextCursor })
    },

    updateFilters: (newFilters) => {
      const { filters } = get()
      set({ 
        filters: { ...filters, ...newFilters },
        // Reset feed when filters change
        items: [],
        currentIndex: 0,
        nextCursor: undefined,
        hasMore: true
      })
    },

    setCurrentIndex: (index) => {
      const { items } = get()
      if (index >= 0 && index < items.length) {
        set({ currentIndex: index })
      }
    },

    addPreloadedItem: (postId) => {
      const { preloadedItems } = get()
      set({ 
        preloadedItems: new Set([...preloadedItems, postId])
      })
    },

    clearFeed: () => {
      set({
        items: [],
        currentIndex: 0,
        nextCursor: undefined,
        hasMore: true,
        loading: false,
        error: undefined,
        preloadedItems: new Set(),
      })
    },

    reset: () => {
      set(initialState)
    },
  }))
)

// Selectors
export const selectCurrentItem = (state: FeedStore) => 
  state.items[state.currentIndex]

export const selectItemByPostId = (postId: string) => (state: FeedStore) =>
  state.items.find(item => item.post.id === postId)

export const selectFilteredItems = (state: FeedStore) => {
  const { items, filters } = state
  
  return items.filter(item => {
    // Filter by moods
    if (filters.moods?.length) {
      const hasMatchingMood = item.post.moods.some(mood => 
        filters.moods!.includes(mood as any)
      )
      if (!hasMatchingMood) return false
    }

    // Filter by content types
    if (filters.contentTypes?.length) {
      if (!filters.contentTypes.includes(item.post.contentType)) {
        return false
      }
    }

    // Filter by experimental content
    if (filters.isExperimental !== undefined) {
      if (item.post.isExperimental !== filters.isExperimental) {
        return false
      }
    }

    // Filter by creators
    if (filters.creatorIds?.length && item.post.creatorId) {
      if (!filters.creatorIds.includes(item.post.creatorId)) {
        return false
      }
    }

    // Filter by personas
    if (filters.personaIds?.length && item.post.personaId) {
      if (!filters.personaIds.includes(item.post.personaId)) {
        return false
      }
    }

    return true
  })
}

// Subscribe to feed changes for analytics
useFeedStore.subscribe(
  (state) => state.currentIndex,
  (currentIndex, previousIndex) => {
    const state = useFeedStore.getState()
    const currentItem = state.items[currentIndex]
    
    if (currentItem && currentIndex !== previousIndex) {
      // Track view analytics
      console.log('Feed item viewed:', {
        postId: currentItem.post.id,
        index: currentIndex,
        feedType: state.currentFeed
      })
    }
  }
)
