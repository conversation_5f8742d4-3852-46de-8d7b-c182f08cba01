import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { VideoPlayer, AudioPlayer, MediaSource } from '@/lib/player/types'

interface PlayerInstance {
  id: string
  player: VideoPlayer | AudioPlayer
  type: 'video' | 'audio'
  source?: MediaSource
  metadata?: any
  registeredAt: Date
}

interface PlaylistItem {
  id: string
  source: MediaSource
  metadata?: any
  duration?: number
}

interface Playlist {
  id: string
  name: string
  items: PlaylistItem[]
  currentIndex: number
  shuffle: boolean
  repeat: 'none' | 'one' | 'all'
}

interface PlayerPreferences {
  autoPlay: boolean
  defaultVolume: number
  defaultMuted: boolean
  defaultQuality: string
  enableKeyboardShortcuts: boolean
  enableVisualizer: boolean
  equalizerPreset: string
  crossfadeDuration: number
}

interface EnhancedPlayerStore {
  // Player instances
  players: Map<string, PlayerInstance>
  currentPlayer: VideoPlayer | AudioPlayer | null
  currentPlayerId: string | null

  // Playlist management
  playlists: Map<string, Playlist>
  currentPlaylist: string | null
  queue: PlaylistItem[]
  history: PlaylistItem[]

  // Global state
  globalVolume: number
  globalMuted: boolean
  isPlaying: boolean
  currentTrack: PlaylistItem | null

  // Preferences
  preferences: PlayerPreferences

  // Actions - Player Management
  registerPlayer: (id: string, player: VideoPlayer | AudioPlayer, type: 'video' | 'audio', metadata?: any) => void
  unregisterPlayer: (id: string) => void
  setCurrentPlayer: (player: VideoPlayer | AudioPlayer | null) => void
  getPlayer: (id: string) => PlayerInstance | undefined
  getAllPlayers: () => PlayerInstance[]
  pauseAllExcept: (exceptPlayer?: VideoPlayer | AudioPlayer | null) => void
  pauseAll: () => void

  // Actions - Playlist Management
  createPlaylist: (name: string, items?: PlaylistItem[]) => string
  deletePlaylist: (id: string) => void
  addToPlaylist: (playlistId: string, item: PlaylistItem) => void
  removeFromPlaylist: (playlistId: string, itemId: string) => void
  reorderPlaylist: (playlistId: string, fromIndex: number, toIndex: number) => void
  setCurrentPlaylist: (playlistId: string | null) => void
  playPlaylist: (playlistId: string, startIndex?: number) => Promise<void>

  // Actions - Queue Management
  addToQueue: (item: PlaylistItem) => void
  removeFromQueue: (itemId: string) => void
  clearQueue: () => void
  playNext: () => Promise<void>
  playPrevious: () => Promise<void>
  shuffle: () => void

  // Actions - Global Controls
  setGlobalVolume: (volume: number) => void
  setGlobalMuted: (muted: boolean) => void
  playTrack: (item: PlaylistItem) => Promise<void>

  // Actions - Preferences
  updatePreferences: (updates: Partial<PlayerPreferences>) => void
  resetPreferences: () => void

  // Utilities
  cleanup: () => void
}

const defaultPreferences: PlayerPreferences = {
  autoPlay: false,
  defaultVolume: 0.8,
  defaultMuted: false,
  defaultQuality: 'auto',
  enableKeyboardShortcuts: true,
  enableVisualizer: true,
  equalizerPreset: 'flat',
  crossfadeDuration: 3000
}

export const usePlayerStore = create<EnhancedPlayerStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    players: new Map(),
    currentPlayer: null,
    currentPlayerId: null,
    playlists: new Map(),
    currentPlaylist: null,
    queue: [],
    history: [],
    globalVolume: 0.8,
    globalMuted: false,
    isPlaying: false,
    currentTrack: null,
    preferences: { ...defaultPreferences },

    // Player Management
    registerPlayer: (id, player, type, metadata) => {
      set((state) => {
        const newPlayers = new Map(state.players)
        newPlayers.set(id, {
          id,
          player,
          type,
          metadata,
          registeredAt: new Date()
        })
        return { players: newPlayers }
      })
    },

    unregisterPlayer: (id) => {
      set((state) => {
        const newPlayers = new Map(state.players)
        const playerInstance = newPlayers.get(id)
        
        if (playerInstance) {
          // Clean up if this was the current player
          const updates: Partial<EnhancedPlayerStore> = {}
          if (state.currentPlayer === playerInstance.player) {
            updates.currentPlayer = null
            updates.currentPlayerId = null
            updates.isPlaying = false
          }
          
          newPlayers.delete(id)
          return { players: newPlayers, ...updates }
        }
        
        return state
      })
    },

    setCurrentPlayer: (player) => {
      set((state) => {
        // Find the player ID
        let playerId: string | null = null
        for (const [id, instance] of state.players) {
          if (instance.player === player) {
            playerId = id
            break
          }
        }

        return {
          currentPlayer: player,
          currentPlayerId: playerId
        }
      })
    },

    getPlayer: (id) => {
      return get().players.get(id)
    },

    getAllPlayers: () => {
      return Array.from(get().players.values())
    },

    pauseAllExcept: (exceptPlayer) => {
      const { players } = get()
      players.forEach((instance) => {
        if (instance.player !== exceptPlayer) {
          instance.player.pause()
        }
      })
    },

    pauseAll: () => {
      const { players } = get()
      players.forEach((instance) => {
        instance.player.pause()
      })
      set({ isPlaying: false })
    },

    // Playlist Management
    createPlaylist: (name, items = []) => {
      const id = `playlist-${Date.now()}`
      set((state) => {
        const newPlaylists = new Map(state.playlists)
        newPlaylists.set(id, {
          id,
          name,
          items,
          currentIndex: 0,
          shuffle: false,
          repeat: 'none'
        })
        return { playlists: newPlaylists }
      })
      return id
    },

    deletePlaylist: (id) => {
      set((state) => {
        const newPlaylists = new Map(state.playlists)
        newPlaylists.delete(id)
        
        const updates: Partial<EnhancedPlayerStore> = { playlists: newPlaylists }
        if (state.currentPlaylist === id) {
          updates.currentPlaylist = null
        }
        
        return updates
      })
    },

    addToPlaylist: (playlistId, item) => {
      set((state) => {
        const newPlaylists = new Map(state.playlists)
        const playlist = newPlaylists.get(playlistId)
        
        if (playlist) {
          playlist.items.push(item)
          newPlaylists.set(playlistId, playlist)
        }
        
        return { playlists: newPlaylists }
      })
    },

    removeFromPlaylist: (playlistId, itemId) => {
      set((state) => {
        const newPlaylists = new Map(state.playlists)
        const playlist = newPlaylists.get(playlistId)
        
        if (playlist) {
          playlist.items = playlist.items.filter(item => item.id !== itemId)
          newPlaylists.set(playlistId, playlist)
        }
        
        return { playlists: newPlaylists }
      })
    },

    reorderPlaylist: (playlistId, fromIndex, toIndex) => {
      set((state) => {
        const newPlaylists = new Map(state.playlists)
        const playlist = newPlaylists.get(playlistId)
        
        if (playlist) {
          const items = [...playlist.items]
          const [movedItem] = items.splice(fromIndex, 1)
          items.splice(toIndex, 0, movedItem)
          
          playlist.items = items
          newPlaylists.set(playlistId, playlist)
        }
        
        return { playlists: newPlaylists }
      })
    },

    setCurrentPlaylist: (playlistId) => {
      set({ currentPlaylist: playlistId })
    },

    playPlaylist: async (playlistId, startIndex = 0) => {
      const { playlists, playTrack } = get()
      const playlist = playlists.get(playlistId)
      
      if (playlist && playlist.items[startIndex]) {
        set({ currentPlaylist: playlistId })
        
        // Update playlist current index
        const newPlaylists = new Map(playlists)
        playlist.currentIndex = startIndex
        newPlaylists.set(playlistId, playlist)
        set({ playlists: newPlaylists })
        
        await playTrack(playlist.items[startIndex])
      }
    },

    // Queue Management
    addToQueue: (item) => {
      set((state) => ({
        queue: [...state.queue, item]
      }))
    },

    removeFromQueue: (itemId) => {
      set((state) => ({
        queue: state.queue.filter(item => item.id !== itemId)
      }))
    },

    clearQueue: () => {
      set({ queue: [] })
    },

    playNext: async () => {
      const { queue, currentPlaylist, playlists, playTrack } = get()
      
      // Check queue first
      if (queue.length > 0) {
        const nextItem = queue[0]
        set((state) => ({
          queue: state.queue.slice(1)
        }))
        await playTrack(nextItem)
        return
      }
      
      // Check current playlist
      if (currentPlaylist) {
        const playlist = playlists.get(currentPlaylist)
        if (playlist) {
          const nextIndex = playlist.currentIndex + 1
          if (nextIndex < playlist.items.length) {
            await get().playPlaylist(currentPlaylist, nextIndex)
          } else if (playlist.repeat === 'all') {
            await get().playPlaylist(currentPlaylist, 0)
          }
        }
      }
    },

    playPrevious: async () => {
      const { currentPlaylist, playlists } = get()
      
      if (currentPlaylist) {
        const playlist = playlists.get(currentPlaylist)
        if (playlist) {
          const prevIndex = playlist.currentIndex - 1
          if (prevIndex >= 0) {
            await get().playPlaylist(currentPlaylist, prevIndex)
          } else if (playlist.repeat === 'all') {
            await get().playPlaylist(currentPlaylist, playlist.items.length - 1)
          }
        }
      }
    },

    shuffle: () => {
      const { currentPlaylist, playlists } = get()
      
      if (currentPlaylist) {
        set((state) => {
          const newPlaylists = new Map(state.playlists)
          const playlist = newPlaylists.get(currentPlaylist)
          
          if (playlist) {
            // Fisher-Yates shuffle
            const items = [...playlist.items]
            for (let i = items.length - 1; i > 0; i--) {
              const j = Math.floor(Math.random() * (i + 1))
              ;[items[i], items[j]] = [items[j], items[i]]
            }
            
            playlist.items = items
            playlist.currentIndex = 0
            playlist.shuffle = !playlist.shuffle
            newPlaylists.set(currentPlaylist, playlist)
          }
          
          return { playlists: newPlaylists }
        })
      }
    },

    // Global Controls
    setGlobalVolume: (volume) => {
      set({ globalVolume: volume })
      
      // Apply to current player
      const { currentPlayer } = get()
      if (currentPlayer) {
        currentPlayer.setVolume(volume)
      }
    },

    setGlobalMuted: (muted) => {
      set({ globalMuted: muted })
      
      // Apply to current player
      const { currentPlayer } = get()
      if (currentPlayer) {
        currentPlayer.setMuted(muted)
      }
    },

    playTrack: async (item) => {
      const { currentPlayer, history } = get()
      
      if (currentPlayer) {
        try {
          await currentPlayer.load(item.source)
          await currentPlayer.play()
          
          set({
            currentTrack: item,
            isPlaying: true,
            history: [item, ...history.slice(0, 49)] // Keep last 50 items
          })
        } catch (error) {
          console.error('Failed to play track:', error)
        }
      }
    },

    // Preferences
    updatePreferences: (updates) => {
      set((state) => ({
        preferences: { ...state.preferences, ...updates }
      }))
    },

    resetPreferences: () => {
      set({ preferences: { ...defaultPreferences } })
    },

    // Utilities
    cleanup: () => {
      const { players } = get()
      players.forEach((instance) => {
        instance.player.destroy()
      })
      
      set({
        players: new Map(),
        currentPlayer: null,
        currentPlayerId: null,
        isPlaying: false,
        currentTrack: null
      })
    }
  }))
)

// Selectors
export const selectCurrentPlayer = () => usePlayerStore.getState().currentPlayer
export const selectIsPlaying = () => usePlayerStore.getState().isPlaying
export const selectCurrentTrack = () => usePlayerStore.getState().currentTrack
export const selectQueue = () => usePlayerStore.getState().queue
export const selectPlaylists = () => Array.from(usePlayerStore.getState().playlists.values())
export const selectPreferences = () => usePlayerStore.getState().preferences
