import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { FeedPreferences, FeedType } from '@/types/feed'
import { MoodType } from '@/types'

interface UserPreferencesStore extends FeedPreferences {
  // Actions
  setDefaultFeedType: (feedType: FeedType) => void
  setAutoPlay: (autoPlay: boolean) => void
  setShowCaptions: (showCaptions: boolean) => void
  setPreferredMoods: (moods: MoodType[]) => void
  addPreferredMood: (mood: MoodType) => void
  removePreferredMood: (mood: MoodType) => void
  blockCreator: (creatorId: string) => void
  unblockCreator: (creatorId: string) => void
  updateNotificationSettings: (settings: Partial<FeedPreferences['notificationSettings']>) => void
  reset: () => void
}

const defaultPreferences: FeedPreferences = {
  defaultFeedType: FeedType.DISCOVER,
  autoPlay: true,
  showCaptions: false,
  preferredMoods: [],
  blockedCreators: [],
  notificationSettings: {
    newFollowerContent: true,
    moodMatches: true,
    trending: false,
  },
}

export const useUserPreferencesStore = create<UserPreferencesStore>()(
  persist(
    (set, get) => ({
      ...defaultPreferences,

      setDefaultFeedType: (feedType) => {
        set({ defaultFeedType: feedType })
      },

      setAutoPlay: (autoPlay) => {
        set({ autoPlay })
      },

      setShowCaptions: (showCaptions) => {
        set({ showCaptions })
      },

      setPreferredMoods: (moods) => {
        set({ preferredMoods: moods })
      },

      addPreferredMood: (mood) => {
        const { preferredMoods } = get()
        if (!preferredMoods.includes(mood)) {
          set({ preferredMoods: [...preferredMoods, mood] })
        }
      },

      removePreferredMood: (mood) => {
        const { preferredMoods } = get()
        set({ 
          preferredMoods: preferredMoods.filter(m => m !== mood)
        })
      },

      blockCreator: (creatorId) => {
        const { blockedCreators } = get()
        if (!blockedCreators.includes(creatorId)) {
          set({ blockedCreators: [...blockedCreators, creatorId] })
        }
      },

      unblockCreator: (creatorId) => {
        const { blockedCreators } = get()
        set({ 
          blockedCreators: blockedCreators.filter(id => id !== creatorId)
        })
      },

      updateNotificationSettings: (settings) => {
        const { notificationSettings } = get()
        set({ 
          notificationSettings: { ...notificationSettings, ...settings }
        })
      },

      reset: () => {
        set(defaultPreferences)
      },
    }),
    {
      name: 'hvppy-user-preferences',
      version: 1,
    }
  )
)
