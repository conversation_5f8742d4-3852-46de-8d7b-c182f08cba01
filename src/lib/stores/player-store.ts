import { create } from 'zustand'
import { VideoPlayerState } from '@/types/feed'

interface PlayerStore extends VideoPlayerState {
  // Actions
  setCurrentPost: (postId: string) => void
  play: () => void
  pause: () => void
  togglePlay: () => void
  setMuted: (muted: boolean) => void
  toggleMute: () => void
  setVolume: (volume: number) => void
  setCurrentTime: (time: number) => void
  setDuration: (duration: number) => void
  setBuffering: (buffering: boolean) => void
  setPlaybackRate: (rate: number) => void
  
  // Player management
  registerPlayer: (postId: string, element: HTMLVideoElement | HTMLAudioElement) => void
  unregisterPlayer: (postId: string) => void
  getPlayer: (postId: string) => HTMLVideoElement | HTMLAudioElement | null
  
  // Auto-play management
  pauseAll: () => void
  playPost: (postId: string) => void
  
  reset: () => void
}

const initialState: VideoPlayerState = {
  currentPostId: undefined,
  isPlaying: false,
  isMuted: true, // Start muted for better UX
  volume: 0.8,
  currentTime: 0,
  duration: 0,
  isBuffering: false,
  playbackRate: 1,
}

// Store player elements outside of Zustand to avoid serialization issues
const playerElements = new Map<string, HTMLVideoElement | HTMLAudioElement>()

export const usePlayerStore = create<PlayerStore>((set, get) => ({
  ...initialState,

  setCurrentPost: (postId) => {
    const { currentPostId } = get()
    
    // Pause previous player
    if (currentPostId && currentPostId !== postId) {
      const prevPlayer = playerElements.get(currentPostId)
      if (prevPlayer) {
        prevPlayer.pause()
      }
    }
    
    set({ 
      currentPostId: postId,
      currentTime: 0,
      isBuffering: false 
    })
  },

  play: () => {
    const { currentPostId } = get()
    if (currentPostId) {
      const player = playerElements.get(currentPostId)
      if (player) {
        player.play().catch(console.error)
        set({ isPlaying: true })
      }
    }
  },

  pause: () => {
    const { currentPostId } = get()
    if (currentPostId) {
      const player = playerElements.get(currentPostId)
      if (player) {
        player.pause()
        set({ isPlaying: false })
      }
    }
  },

  togglePlay: () => {
    const { isPlaying } = get()
    if (isPlaying) {
      get().pause()
    } else {
      get().play()
    }
  },

  setMuted: (muted) => {
    const { currentPostId } = get()
    if (currentPostId) {
      const player = playerElements.get(currentPostId)
      if (player) {
        player.muted = muted
        set({ isMuted: muted })
      }
    }
  },

  toggleMute: () => {
    const { isMuted } = get()
    get().setMuted(!isMuted)
  },

  setVolume: (volume) => {
    const { currentPostId } = get()
    const clampedVolume = Math.max(0, Math.min(1, volume))
    
    if (currentPostId) {
      const player = playerElements.get(currentPostId)
      if (player) {
        player.volume = clampedVolume
        set({ volume: clampedVolume })
        
        // Unmute if volume is set above 0
        if (clampedVolume > 0 && player.muted) {
          get().setMuted(false)
        }
      }
    }
  },

  setCurrentTime: (time) => {
    set({ currentTime: time })
  },

  setDuration: (duration) => {
    set({ duration })
  },

  setBuffering: (buffering) => {
    set({ isBuffering: buffering })
  },

  setPlaybackRate: (rate) => {
    const { currentPostId } = get()
    if (currentPostId) {
      const player = playerElements.get(currentPostId)
      if (player) {
        player.playbackRate = rate
        set({ playbackRate: rate })
      }
    }
  },

  registerPlayer: (postId, element) => {
    playerElements.set(postId, element)
    
    // Set up event listeners
    const updateTime = () => {
      if (get().currentPostId === postId) {
        set({ currentTime: element.currentTime })
      }
    }
    
    const updateDuration = () => {
      if (get().currentPostId === postId) {
        set({ duration: element.duration || 0 })
      }
    }
    
    const updateBuffering = () => {
      if (get().currentPostId === postId) {
        set({ isBuffering: element.readyState < 3 })
      }
    }
    
    const updatePlayState = () => {
      if (get().currentPostId === postId) {
        set({ isPlaying: !element.paused })
      }
    }
    
    element.addEventListener('timeupdate', updateTime)
    element.addEventListener('durationchange', updateDuration)
    element.addEventListener('loadstart', updateBuffering)
    element.addEventListener('canplay', updateBuffering)
    element.addEventListener('play', updatePlayState)
    element.addEventListener('pause', updatePlayState)
    
    // Apply current settings
    const { volume, isMuted, playbackRate } = get()
    element.volume = volume
    element.muted = isMuted
    element.playbackRate = playbackRate
  },

  unregisterPlayer: (postId) => {
    const element = playerElements.get(postId)
    if (element) {
      // Remove event listeners
      element.removeEventListener('timeupdate', () => {})
      element.removeEventListener('durationchange', () => {})
      element.removeEventListener('loadstart', () => {})
      element.removeEventListener('canplay', () => {})
      element.removeEventListener('play', () => {})
      element.removeEventListener('pause', () => {})
      
      playerElements.delete(postId)
    }
  },

  getPlayer: (postId) => {
    return playerElements.get(postId) || null
  },

  pauseAll: () => {
    playerElements.forEach((player) => {
      player.pause()
    })
    set({ isPlaying: false })
  },

  playPost: (postId) => {
    // Pause all other players
    get().pauseAll()
    
    // Set current post and play
    get().setCurrentPost(postId)
    
    // Small delay to ensure the post is set
    setTimeout(() => {
      get().play()
    }, 100)
  },

  reset: () => {
    // Pause all players
    get().pauseAll()
    
    // Clear player elements
    playerElements.clear()
    
    // Reset state
    set(initialState)
  },
}))

// Selectors
export const selectIsCurrentPost = (postId: string) => (state: PlayerStore) =>
  state.currentPostId === postId

export const selectPlayerForPost = (postId: string) => () =>
  playerElements.get(postId) || null
