import { account } from './appwrite'
import { ID } from 'appwrite'
import { prisma } from './prisma'
import { UserRole } from '@/types'

export interface CreateUserParams {
  email: string
  password: string
  username: string
  displayName?: string
}

export interface LoginParams {
  email: string
  password: string
}

export class AuthService {
  // Create new user account
  static async createAccount({ email, password, username, displayName }: CreateUserParams) {
    try {
      // Create user in Appwrite
      const appwriteUser = await account.create(
        ID.unique(),
        email,
        password,
        displayName || username
      )

      // Create user in our database
      const user = await prisma.user.create({
        data: {
          appwriteId: appwriteUser.$id,
          email,
          username,
          displayName: displayName || username,
          role: UserRole.FAN,
        },
      })

      // Create session
      await account.createEmailPasswordSession(email, password)

      return { user, appwriteUser }
    } catch (error) {
      console.error('Error creating account:', error)
      throw error
    }
  }

  // Login user
  static async login({ email, password }: LoginParams) {
    try {
      const session = await account.createEmailPasswordSession(email, password)
      return session
    } catch (error) {
      console.error('Error logging in:', error)
      throw error
    }
  }

  // Logout user
  static async logout() {
    try {
      await account.deleteSession('current')
    } catch (error) {
      console.error('Error logging out:', error)
      throw error
    }
  }

  // Get current user
  static async getCurrentUser() {
    try {
      const appwriteUser = await account.get()
      
      // Get user from our database
      const user = await prisma.user.findUnique({
        where: { appwriteId: appwriteUser.$id },
        include: {
          creator: {
            include: {
              personas: true,
            },
          },
        },
      })

      return { appwriteUser, user }
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }

  // Update user profile
  static async updateProfile(userId: string, data: Partial<{
    displayName: string
    bio: string
    avatar: string
  }>) {
    try {
      const user = await prisma.user.update({
        where: { id: userId },
        data,
      })
      return user
    } catch (error) {
      console.error('Error updating profile:', error)
      throw error
    }
  }

  // Create creator profile
  static async createCreatorProfile(userId: string, data: {
    stageName?: string
    genre: string[]
    location?: string
    website?: string
    socialLinks?: Record<string, string>
  }) {
    try {
      // Update user role to creator
      await prisma.user.update({
        where: { id: userId },
        data: { role: UserRole.CREATOR },
      })

      // Create creator profile
      const creator = await prisma.creator.create({
        data: {
          userId,
          ...data,
        },
      })

      return creator
    } catch (error) {
      console.error('Error creating creator profile:', error)
      throw error
    }
  }

  // Verify email
  static async verifyEmail(userId: string, secret: string) {
    try {
      await account.updateVerification(userId, secret)
      
      // Update user verification status in our database
      await prisma.user.update({
        where: { appwriteId: userId },
        data: { isVerified: true },
      })
    } catch (error) {
      console.error('Error verifying email:', error)
      throw error
    }
  }

  // Send password recovery email
  static async recoverPassword(email: string) {
    try {
      await account.createRecovery(
        email,
        `${process.env.NEXTAUTH_URL}/auth/reset-password`
      )
    } catch (error) {
      console.error('Error sending recovery email:', error)
      throw error
    }
  }

  // Reset password
  static async resetPassword(userId: string, secret: string, password: string) {
    try {
      await account.updateRecovery(userId, secret, password)
    } catch (error) {
      console.error('Error resetting password:', error)
      throw error
    }
  }
}
