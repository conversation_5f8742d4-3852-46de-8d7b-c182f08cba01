# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
APPWRITE_API_KEY=your_api_key

# Database Configuration (PostgreSQL)
DATABASE_URL="postgresql://username:password@localhost:5432/hvppy_central?schema=public"

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Next.js Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Environment
NODE_ENV=development

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760 # 10MB in bytes
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,video/mp4,audio/mpeg,audio/wav

# AI Configuration
NEXT_PUBLIC_AI_MODEL=gpt-4-turbo-preview
NEXT_PUBLIC_ENABLE_AI_FEATURES=true

# Feature Flags
NEXT_PUBLIC_ENABLE_EXPERIMENTAL_FEATURES=false
NEXT_PUBLIC_ENABLE_ANALYTICS=true
